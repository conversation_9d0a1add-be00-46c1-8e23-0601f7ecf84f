import logging
import sys
import os
from datetime import datetime
import datetime
from collections import deque
import random
import string

from wecom_bot_svr import WecomBotServer, RspTextMsg, RspMarkdownMsg, ReqMsg
from wecom_bot_svr.req_msg import TextReqMsg

from common.logs.logger import app_logger
from common.client.wecom_client import WecomClient
from common.wecom.wecom_bot_tips import WecomBotTips
from common.wecom.wecom_bot import WecomBot

from common.tools.file_utils import append_to_file
from common.error.detailed_value_error import DetailedValueError

# 参考链接：https://developer.work.weixin.qq.com/document/path/99399

# 记录已处理消息
processed_msg_ids = deque(maxlen=100)

def msg_handler(req_msg: ReqMsg, server: WecomBotServer):
    ret = RspMarkdownMsg()
    app_logger.info(f'用户输入：{req_msg.content}')
    app_logger.info(f'会话id：{req_msg.chat_id}')
    app_logger.info(f'用户id：{req_msg.from_user.en_name}')
    app_logger.info(f'消息ID：{req_msg.msg_id}')
    ticket_id = generate_ticket_id()
    append_to_file(ticket_id, f'>>> 用户输入\n{req_msg.content}\n')
    append_to_file(ticket_id, f'>>> 用户id\n{req_msg.from_user.en_name}\n')
    append_to_file(ticket_id, f'>>> 工单id\n{ticket_id}\n')

    msg_id = req_msg.msg_id
    if msg_id in processed_msg_ids:
        app_logger.info(f'企微机器人重试机制。消息正在处理，跳过')
        ret.content = WecomBotTips.retry_text()
        return ret
    # 将消息ID添加到已处理集合中
    processed_msg_ids.append(msg_id)
    app_logger.info(f'已处理消息：{processed_msg_ids}')

    if req_msg.msg_type == 'text' and isinstance(req_msg, TextReqMsg):
        if req_msg.content.strip().lower().startswith('【日志分析】') and server is not None:
            try:
                wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
                is_rewrite_prompt, user_query_scene, url = wecom_bot.parse_scene_and_is_rewrite_prompt(ticket_id)
                if is_rewrite_prompt:
                    ret.content = f'已为你找到prompt文件链接，请按照文档里的模版提示改写：\n场景：{user_query_scene} -> [{url}]({url})'
                    return ret
                else:
                    WecomBotTips.webhook_send(req_msg.chat_id, "开始处理您的需求，请稍等...")
                    # 获取日志分析结果
                    wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
                    result_save_path = wecom_bot.analyze_log(ticket_id)
                    if result_save_path:
                        server.send_file(req_msg.chat_id, result_save_path)
                        WecomBotTips.send_evaluate_and_save_prompt(chat_id=req_msg.chat_id, ticket_id=ticket_id)
                        return RspTextMsg()
                    else:
                        ret.content = WecomBotTips.help_markdown()
                        return ret
            except Exception as e:
                ret.content = f'{e.args[0]}'
                return ret

        elif req_msg.content.strip().lower().startswith('【满意度回访】') and server is not None:
            try:
                wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
                analyze_process_path = wecom_bot.save_evaluate()
                if analyze_process_path:
                    server.send_file(req_msg.chat_id, analyze_process_path)
                    return RspTextMsg()
                else:
                    ret.content = "谢谢您的评价"
                    return ret
            except DetailedValueError as e:
                ret.content = f'{e.args[0]}'
                return ret
        elif req_msg.content.strip() == '1' and server is not None:
            # 日志工具
            ret.content = WecomBotTips.log_tool_help_markdown()
            return ret
        elif req_msg.content.strip() == '2' and server is not None:
            # 版本工具
            ret.content = WecomBotTips.version_info_help_markdown()
            return ret
        elif req_msg.content.strip() == '3' and server is not None:
            # 灰度实验数据分析
            ret.content = WecomBotTips.gray_data_help_markdown()
            return ret
        elif req_msg.content.strip().lower().startswith('【灰度实验数据分析】') and server is not None:
            # 灰度数据分析
            WecomBotTips.webhook_send(req_msg.chat_id, "开始处理您的需求，请稍等...")
            wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
            iwiki_url = wecom_bot.gray_data_analysis()
            content = f'已为你生成灰度分析数据，iwiki链接：[{iwiki_url}]({iwiki_url})'
            WecomBotTips.webhook_send(req_msg.chat_id, content)
            ret.content = ""
            return ret
        elif req_msg.content.strip() == '4' and server is not None:
            # 版本需求列表
            ret.content = WecomBotTips.version_mr_help_markdown()
            return ret
        elif req_msg.content.strip().lower().startswith('【版本需求列表】') and server is not None:
            # 灰度数据分析
            WecomBotTips.webhook_send(req_msg.chat_id, "开始处理您的需求，请稍等...")
            wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
            iwiki_url = wecom_bot.version_mr_collect()
            content = f'已为你生成版本需求列表，iwiki链接：[{iwiki_url}]({iwiki_url})'
            WecomBotTips.webhook_send(req_msg.chat_id, content)
            ret.content = ""
            return ret


    # 返回默认帮助信息
    ret.content = WecomBotTips.help_markdown()
    return ret

# 事件消息
# 参考链接：https://developer.work.weixin.qq.com/document/path/91881
def event_handler(req_msg):
    app_logger.info(f'事件消息: {req_msg.event_type}')
    ret = RspMarkdownMsg()
    # 项目框架只实现了出群事件处理
    if req_msg.event_type == 'add_to_chat':  # 入群事件处理
        print('add_to_chat')
        app_logger.info(f'机器人加入群聊：{req_msg.chat_id}')
        ret.content = WecomBotTips.help_markdown()
        # ret.content = f'msg_type: {req_msg.msg_type}\n群会话ID: {req_msg.chat_id}\n查询用法请回复: help'
    elif req_msg.event_type == 'delete_from_chat':  # 出群事件处理 待开发
        print('delete_from_chat')
        # ret.content = WecomBotTips.delete_markdown()
    elif req_msg.event_type == 'enter_chat':  # 进入会话事件处理 待开发
        print('enter_chat')
        app_logger.info(f'用户id：{req_msg.from_user.en_name}, action: enter_chat')
        # ret.content = WecomBotTips.help_markdown()
    return ret

def generate_ticket_id():
    date_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    ticket_id = f"{date_str}-{random_str}"
    return ticket_id

def main():
    # URL 填写的URL需要正确响应企业微信验证URL的请求
    # http://api-idc.sgw.woa.com/ebus/yyb_ai/log_wecom_bot
    logging.basicConfig(stream=sys.stdout)
    logging.getLogger().setLevel(logging.INFO)

    token = '4rwXKaSJ9Tvh2rboLAsAJ5P'
    aes_key = 'xbjt1vsILWYRtF7R9emM5nwt58dMMUJFaOC9pxzvlIF'
    corp_id = ''
    host = '0.0.0.0'
    port = 5001
    # https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a87f69e7-c781-4ce0-a556-308a985ce5c9
    bot_key = 'a87f69e7-c781-4ce0-a556-308a985ce5c9'  # 机器人配置中的webhook key

    # 这里要跟机器人名字一样，用于切分群组聊天中的@消息
    bot_name = 'Ai-Log-Bot'
    server = WecomBotServer(name=bot_name, host=host, port=port, path='/log_wecom_bot', token=token, aes_key=aes_key, corp_id=corp_id,
                            bot_key=bot_key)

    server.set_message_handler(msg_handler)
    server.set_event_handler(event_handler)
    server.run()


if __name__ == '__main__':
    main()
