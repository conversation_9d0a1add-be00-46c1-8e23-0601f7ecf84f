import os
from datetime import datetime, timedelta
import tempfile

from common.logs.logger import app_logger
from log_tool.agent.filter_user_action import FilterUserAction
from common.client.fetch_log_client import FetchLogClient
from common.tools.text_parse_utils import TextParseUtils, LogAnalyzeConfig, EvaluateConfig, GrayDataAnalysisConfig
from common.tools.file_utils import append_to_file
from common.error.detailed_value_error import raise_value_error, ErrorCode
from log_tool.agent.user_intent_parser_agent import UserIntentParser
from common.logs.loggers.models import Evaluate, Event
from common.logs.loggers.evaluate_logger import evaluate_logger
from common.logs.loggers.event_logger import event_logger
from version_tool.gray_data.gray_data_collector import auto_collect_gray_data
from version_tool.version_mr_collector import collect_version_mr_info

OUTPUT_DIR = 'output/'

class WecomBot():
    def __init__(self, user_input, user_name, user_cn_name):
        self._user_input = user_input
        self._user_name = user_name
        self._user_cn_name = user_cn_name
    
    def parse_scene_and_is_rewrite_prompt(self, ticket_id):
        parse_result = TextParseUtils.parse(self._user_input, LogAnalyzeConfig)
        parse_json = parse_result.to_json()
        print(f'parse_json = {parse_json}')
        is_rewrite_prompt = parse_result.is_rewrite_prompt
        # 是否需要重写Prompt
        if is_rewrite_prompt == "是":
            now = datetime.now()
            event = Event(
                timestamp=now.strftime("%Y%m%d-%H%M%S"),
                event_type="rewrite_prompt",
                en_name=self._user_name,
                cn_name=self._user_cn_name,
                event_params={"ticket_id": ticket_id}
            )
            event_logger.write_event(event)
            # 重写Prompt，返回对应场景的iwiki链接
            # 查询预设场景配置
            user_query_scene, url = UserIntentParser.parse_user_intent(query=parse_result.query, ticket_id=ticket_id)
            return True, user_query_scene, url
        # 不重写
        else:
            return False, "", ""

    # 分析日志
    def analyze_log(self, ticket_id):
        now = datetime.now()
        event = Event(
            timestamp=now.strftime("%Y%m%d-%H%M%S"),
            event_type="bot_analyze",
            en_name=self._user_name,
            cn_name=self._user_cn_name,
            event_params={"ticket_id": ticket_id}
        )
        event_logger.write_event(event)

        # 解析用户输入
        parse_result = TextParseUtils.parse(self._user_input, LogAnalyzeConfig)

        with tempfile.TemporaryDirectory() as tmpdirname:
            print('临时文件夹路径:', tmpdirname)
            # 获取日志文件路径
            fetch_log = FetchLogClient(download_link=parse_result.log_link, log_save_path=tmpdirname)
            logs_path = fetch_log.fetch_log_from_url()

            if not logs_path:
                app_logger.info("未找到日志文件")
                return None

            # 分析日志
            print("AI 开始分析 您的日志... ")
            # raise_value_error(ErrorCode.MODEL_OVERLOAD, message="混元模型过载，请重试")
            try:
                filter_user_action = FilterUserAction(
                    query=parse_result.query,
                    logs_path=logs_path,  
                    bug_time=parse_result.bug_time,
                    ticket_id=ticket_id
                )
                result_stream = filter_user_action.parse_user_action()
                if not result_stream:
                    app_logger.info("日志返回结果为空 -- 混元模型过载，请重试")
                    raise_value_error(ErrorCode.MODEL_OVERLOAD, message=f"混元模型过载，请重试")

                # 最终回答结果 保存路径
                result_answer_save_path = ''
                # 思考过程 保存路径
                result_thinking_save_path = ''
                # 思考过程数据
                result_thinking_data = ''
                # 回答结果
                result_answer_data = ''
                for result in result_stream:
                    if result['type'] == 'result_answer' and result['data']:
                        result_answer_data = result['data']
                        result_answer_save_path =  self._save_result_to_file(result['data'], '日志分析结果')
                    elif result['type'] == 'result_answer' and result['data'] is None:
                        app_logger.warming("日志分析结果为空 -- 日志分析失败")
                    elif result['type'] == 'reasoning_content' and result['data']=="混元模型过载，请重试":
                        result_answer_save_path =  self._save_result_to_file("混元模型过载，请重试", '日志分析结果')
                    else:
                        result_thinking_data += result['data']

                # result_thinking_save_path =  self._save_result_to_file(result_thinking_data, '思考过程')

                append_to_file(ticket_id, f'>>> 思考过程\n{result_thinking_data}\n')
                append_to_file(ticket_id, f'>>> 回答内容\n{result_answer_data}\n')

                # app_logger.info(f"日志分析完成，思考过程 保存路径：{result_thinking_save_path}")
                app_logger.info(f"日志分析完成，最终结果 保存路径：{result_answer_save_path}")
            except Exception as e:
                app_logger.error(f"日志分析失败：{e}")
                content = f'{e.args[0]}'
                raise_value_error(ErrorCode.MODEL_OVERLOAD, message=content)

        return result_answer_save_path
    
    # 将分析结果保存为文件
    def _save_result_to_file(self, result, path_suffix):
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        os.makedirs(OUTPUT_DIR, exist_ok=True)
        save_path = os.path.join(OUTPUT_DIR, f'{self._user_name}_{path_suffix}_{current_time}.md')
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(result)
        return save_path

    def save_evaluate(self):
        # 解析用户输入
        parse_result = TextParseUtils.parse(self._user_input, EvaluateConfig)
        ticket_id = parse_result.ticket_id
        evaluate = parse_result.evaluate
        is_send_analyze_process = parse_result.is_send_analyze_process
        if is_send_analyze_process == "是":
            is_send_analyze_process = True
        elif is_send_analyze_process == "否": 
            is_send_analyze_process = False
        if evaluate:
            now = datetime.now()
            # 记录事件
            event = Event(
                timestamp=now.strftime("%Y%m%d-%H%M%S"),
                event_type="evaluate",
                en_name=self._user_name,
                cn_name=self._user_cn_name,
                event_params={"ticket_id": ticket_id,
                              "evaluate": evaluate}
            )
            event_logger.write_event(event)
            # 记录评价
            evaluate_data = Evaluate(
                timestamp=now.strftime("%Y%m%d-%H%M%S"),
                en_name=self._user_name,
                cn_name=self._user_cn_name,
                ticket_id=ticket_id,
                evaluate=evaluate
            )
            evaluate_logger.write_evaluate(evaluate_data)
        if is_send_analyze_process:
            # 根据工单得到 用户日志的分析过程
            path = "/data/workspace/logassistant-server/alayze_logs"
            all_items = os.listdir(path)
            for item in all_items:
                if ticket_id in item:
                    return path + '/' + item


    def gray_data_analysis(self):
        """灰度数据分析"""
        # 解析用户输入
        parse_result = TextParseUtils.parse(self._user_input, GrayDataAnalysisConfig)
        version = parse_result.version
        is_fetch_data = parse_result.is_fetch_data
        iwiki_url = auto_collect_gray_data(version, is_fetch_data)
        return iwiki_url

    def version_mr_collect(self):
        """获取版本需求列表"""
        # 解析用户输入
        parse_result = TextParseUtils.parse(self._user_input, GrayDataAnalysisConfig)
        version = parse_result.version
        is_fetch_data = parse_result.is_fetch_data
        iwiki_url = collect_version_mr_info(version, is_fetch_data)
        return iwiki_url

if __name__ == "__main__":
    user_input = '''
【日志分析】
### 日志链接
https://cms.myapp.com/xy/yybtech/NGIT2mIu.zip
### 用户问题
发货失败
### bug时间
2025-05-13 14:10:00
### 是否需要改写prompt
否
    '''
    wecom_bot = WecomBot(user_input, 'xxx', 'xxx')
    result =  wecom_bot.parse_scene_and_is_rewrite_prompt("111")
    print(f'result = {result}')
    wecom_bot.analyze_log("xxx")

    save_prompt = """
【满意度回访】
### 工单ID
20250513%H4928-5R9B
### 满意度
5星
### 是否需要查看分析过程
是
    """
    wecom_bot = WecomBot(save_prompt, 'xxx', 'xxx')
    path = wecom_bot.save_evaluate()
    print(f'path = {path}')


    # print('result_save_path = ', result_save_path)
