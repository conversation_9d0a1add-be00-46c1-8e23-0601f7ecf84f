from typing import List, Optional, Tuple
from datetime import datetime, timedelta
import openpyxl
from openpyxl.utils import get_column_letter

from common.logs.loggers.base_logger import BaseLogger
from common.logs.loggers.models import Event


class EventLogger(BaseLogger):
    """
    埋点事件日志管理类
    """

    def __init__(self, base_dir: str = "logger_data/events"):
        super().__init__(base_dir)

    def write_event(self, event: Event):
        """
        写入一条事件日志
        :param event: Event对象
        """
        self.write(event.to_dict())

    def read_events(self, date_str: str) -> List[Event]:
        """
        读取指定日期的所有事件，返回 Event 对象列表
        :param date_str: 日期字符串 "YYYYMMDD"
        :return: Event对象列表
        """
        dicts = self.read(date_str)
        return [Event.from_dict(d) for d in dicts]

    def count_events(self, date_str: str, event_type: Optional[str] = None) -> int:
        """
        统计指定日期的事件数量，可按事件类型过滤
        :param date_str: 日期字符串 "YYYYMMDD"
        :param event_type: 事件类型，默认统计所有
        :return: 事件数量
        """
        events = self.read_events(date_str)
        if event_type:
            return sum(1 for e in events if e.event_type == event_type)
        return len(events)

    def count_events_in_range(self, start_date: str, end_date: str, event_type: Optional[str] = None) -> int:
        """
        统计时间范围内的事件数量，可按事件类型过滤
        :param start_date: 起始日期 "YYYYMMDD"
        :param end_date: 结束日期 "YYYYMMDD"
        :param event_type: 事件类型，默认统计所有
        :return: 事件数量
        """
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        count = 0
        for i in range((end_dt - start_dt).days + 1):
            day_str = (start_dt + timedelta(days=i)).strftime("%Y%m%d")
            count += self.count_events(day_str, event_type)
        return count
    
    def query_by_field(
        self,
        start_date: str,
        end_date: str,
        field_name: str,
        field_value,
    ) -> Tuple[int, List[Event]]:
        """
        查询指定时间范围内，指定字段等于指定值的事件数量和事件列表
        """
        start_dt = datetime.strptime(start_date, "%Y%m%d")
        end_dt = datetime.strptime(end_date, "%Y%m%d")
        matched_events = []
        for i in range((end_dt - start_dt).days + 1):
            day_str = (start_dt + timedelta(days=i)).strftime("%Y%m%d")
            events = self.read_events(day_str)
            for event in events:
                # 支持嵌套字段查询，比如 event_params.key
                value = self._get_field_value(event, field_name)
                if value == field_value:
                    matched_events.append(event)
        return len(matched_events), matched_events

    def _get_field_value(self, obj, field_name: str):
        """
        支持简单点的嵌套字段访问，比如 "event_params.key"
        """
        attrs = field_name.split(".")
        val = obj
        for attr in attrs:
            if isinstance(val, dict):
                val = val.get(attr, None)
            else:
                val = getattr(val, attr, None)
            if val is None:
                break
        return val
    
    def export_query_to_excel(
        self,
        start_date: str,
        end_date: str,
        field_name: str,
        field_value,
        excel_path: str,
    ) -> int:
        count, events = self.query_by_field(start_date, end_date, field_name, field_value)
        if count == 0:
            print("没有匹配的数据，Excel文件未生成。")
            return 0

        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "查询结果"

        # 先获取普通字段和字典字段的所有键
        first_obj = events[0]
        base_fields = []
        dict_fields = {}  # 字段名 -> set of keys

        for k, v in vars(first_obj).items():
            if isinstance(v, dict):
                dict_fields[k] = set(v.keys())
            else:
                base_fields.append(k)

        # 扫描所有对象，补充字典字段的所有键
        for obj in events[1:]:
            for dict_field in dict_fields.keys():
                d = getattr(obj, dict_field, {})
                if isinstance(d, dict):
                    dict_fields[dict_field].update(d.keys())

        # 构造表头
        headers = base_fields[:]
        for dict_field, keys in dict_fields.items():
            for key in sorted(keys):
                headers.append(f"{dict_field}.{key}")

        ws.append(headers)

        # 写入数据
        for obj in events:
            row = []
            for field in base_fields:
                val = getattr(obj, field, "")
                row.append(val)
            for dict_field, keys in dict_fields.items():
                d = getattr(obj, dict_field, {})
                for key in sorted(keys):
                    val = d.get(key, "")
                    row.append(val)
            ws.append(row)

        # 调整列宽
        for i, col in enumerate(headers, 1):
            max_length = max(
                len(str(cell.value)) if cell.value else 0
                for cell in ws[get_column_letter(i)]
            )
            adjusted_width = max_length + 2
            ws.column_dimensions[get_column_letter(i)].width = adjusted_width

        wb.save(excel_path)
        print(f"已将 {count} 条数据写入 Excel 文件：{excel_path}")
        return count

event_logger = EventLogger()


if __name__ == "__main__":
    now = datetime.now()

    event = Event(
        timestamp=now.strftime("%Y%m%d-%H%M%S"),
        event_type="click_button",
        event_params={"button_id": "submit_order"}
    )
    event_logger.write_event(event)

    today_str = now.strftime("%Y%m%d")
    events = event_logger.read_events(today_str)
    print(f"{today_str} 事件列表:", events)

    count = event_logger.count_events(today_str, event_type="click_button")
    print(f"{today_str} 点击按钮事件数量:", count)

    count = event_logger.count_events_in_range(
        start_date="20250521",
        end_date="20250522",
        event_type="rewrite_prompt"
    )

    print(f"20250521至20250522重写提示事件数量:", count)

    # 查询
    start = now.strftime("%Y%m%d")
    end = start
    count, events = event_logger.query_by_field(start, end, "event_type", "rewrite_prompt")
    print(f"查询到 {count} 条事件，内容如下:")
    for e in events:
        print(e)
    
    event_logger.export_query_to_excel(
        start_date="20250521",
        end_date="20250616",
        field_name="event_type",
        field_value="bot_analyze",
        excel_path="20250616调用次数.xlsx"
    )
