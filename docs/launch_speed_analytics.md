# 启动速度分析功能文档

## 概述

BeaconAPIClient 新增了启动速度分析功能，支持外部自定义QUA、时间参数，查询指定时间内的新用户和老用户启动速度数据。

## 功能特性

### 1. 支持自定义参数
- **QUA列表**: 支持外部传入自定义QUA列表
- **时间范围**: 可自定义查询时间范围，默认为 2024-11-24 ~ 2025-12-29
- **数据源**: 使用数据源ID 1235751，查询 launch_speed_event 表

### 2. 用户类型区分
- **新用户**: 查询常规启动类型（不带套壳）
  - 常规热启动
  - 常规冷启动
  - 常规外call热启动
  - 常规外call冷启动
  
- **老用户**: 查询套壳启动类型
  - 套壳常规热启动
  - 套壳常规冷启动
  - 套壳常规外call热启动
  - 套壳常规外call冷启动

### 3. 数据聚合
- 每个包的各个启动类型对应一个平均值
- 不按天输出，而是整个时间范围的聚合平均值
- 支持按包名便捷查询

## API 接口

### 主要方法

#### 1. query_launch_speed_analytics()
```python
def query_launch_speed_analytics(self, 
                               qua_list: List[str], 
                               start_date: str = "2024-11-24", 
                               end_date: str = "2025-12-29") -> Dict[str, Dict[str, Dict[str, float]]]
```

**参数:**
- `qua_list`: QUA列表
- `start_date`: 开始日期，格式：YYYY-MM-DD
- `end_date`: 结束日期，格式：YYYY-MM-DD

**返回值:**
```python
{
    "QUA名称": {
        "新用户": {
            "常规热启动": 平均值,
            "常规冷启动": 平均值,
            "常规外call热启动": 平均值,
            "常规外call冷启动": 平均值
        },
        "老用户": {
            "常规热启动": 平均值,
            "常规冷启动": 平均值,
            "常规外call热启动": 平均值,
            "常规外call冷启动": 平均值
        }
    }
}
```

#### 2. get_launch_speed_by_package()
```python
def get_launch_speed_by_package(self, 
                               package_name: str, 
                               start_date: str = "2024-11-24", 
                               end_date: str = "2025-12-29") -> Dict[str, Dict[str, float]]
```

**参数:**
- `package_name`: 包名，如 'com.tencent.android.qqdownloader'
- `start_date`: 开始日期，格式：YYYY-MM-DD
- `end_date`: 结束日期，格式：YYYY-MM-DD

**返回值:**
```python
{
    "新用户": {
        "常规热启动": 平均值,
        "常规冷启动": 平均值,
        "常规外call热启动": 平均值,
        "常规外call冷启动": 平均值
    },
    "老用户": {
        "常规热启动": 平均值,
        "常规冷启动": 平均值,
        "常规外call热启动": 平均值,
        "常规外call冷启动": 平均值
    }
}
```

### 辅助方法

#### 3. get_new_user_launch_speed()
获取新用户启动速度数据，支持单个QUA或聚合所有QUA。

#### 4. get_old_user_launch_speed()
获取老用户启动速度数据，支持单个QUA或聚合所有QUA。

## 使用示例

### 基本用法
```python
from common.client.beacon_client import BeaconAPIClient

client = BeaconAPIClient()

# 查询启动速度分析数据
qua_list = ['TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550']
data = client.query_launch_speed_analytics(
    qua_list=qua_list,
    start_date="2024-11-24",
    end_date="2025-12-29"
)

# 打印结果
for qua, user_data in data.items():
    print(f"QUA: {qua}")
    for user_type, launch_data in user_data.items():
        print(f"  {user_type}:")
        for launch_type, avg_value in launch_data.items():
            print(f"    {launch_type}: {avg_value}ms")
```

### 包名查询
```python
# 根据包名查询
package_data = client.get_launch_speed_by_package(
    package_name="com.tencent.android.qqdownloader"
)

print("包名查询结果:")
for user_type, launch_data in package_data.items():
    print(f"  {user_type}:")
    for launch_type, avg_value in launch_data.items():
        print(f"    {launch_type}: {avg_value}ms")
```

### 聚合查询
```python
# 获取新用户启动速度（聚合所有QUA）
new_user_speed = client.get_new_user_launch_speed(data)
print(f"新用户启动速度（聚合）: {new_user_speed}")

# 获取老用户启动速度（聚合所有QUA）
old_user_speed = client.get_old_user_launch_speed(data)
print(f"老用户启动速度（聚合）: {old_user_speed}")
```

## SQL 映射逻辑

基于用户提供的SQL语句，系统会根据以下条件进行启动类型映射：

### 新用户（crab_shell_type = 2）
- `launch_type=3 and start_type=1 and run_type=2` → 常规热启动
- `launch_type=1 and start_type=1 and run_type=2` → 常规冷启动
- `launch_type=3 and start_type=3 and run_type=2` → 常规外call热启动
- `launch_type=1 and start_type=3 and run_type=2` → 常规外call冷启动

### 老用户（crab_shell_type = 1）
- `launch_type=3 and start_type=1 and run_type=2` → 套壳常规热启动
- `launch_type=1 and start_type=1 and run_type=2` → 套壳常规冷启动
- `launch_type=3 and start_type=3 and run_type=2` → 套壳常规外call热启动
- `launch_type=1 and start_type=3 and run_type=2` → 套壳常规外call冷启动

## 注意事项

1. **包名映射**: 使用包名查询前，需要在 `PACKAGE_NAME_TO_QUA_MAP` 中添加对应的映射关系
2. **数据过滤**: 自动过滤掉非目标启动类型的数据
3. **平均值计算**: 只计算有效值（大于0）的平均值
4. **精度**: 平均值保留2位小数
5. **数据源**: 固定使用数据源ID 1235751
