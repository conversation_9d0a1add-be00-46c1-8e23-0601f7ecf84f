# 日志分析助手

这是一个借助大模型分析应用宝客户端日志的智能分析助手，目前接入企微机器人。

## 使用手册

* 创建虚拟环境

```bash
python3 -m venv .venv
```

* 激活虚拟环境

```bash
source .venv/bin/activate
```

* 安装依赖

`参考文档：https://iwiki.woa.com/p/4008515885`

```
pip install requests
pip install polaris-cpp-py --index-url https://mirrors.cloud.tencent.com/pypi/simple/
pip install sseclient-py==1.7.2 -i https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple --extra-index-url https://mirrors.tencent.com/pypi/simple/
pip install setuptools
pip install wecom_bot_svr
```

### AnyDev（云研发）部署企微机器人，使用示例
```bash
python log_wecom_bot.py
```
