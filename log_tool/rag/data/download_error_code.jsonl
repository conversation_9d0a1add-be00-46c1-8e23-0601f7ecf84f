{"id": "id_1", "query": "retCode:0", "value": "retCode 0 表示 正常"}
{"id": "id_2", "query": "retCode:-1", "value": "retCode -1 表示 超过最大跳转次数返回次失败码"}
{"id": "id_3", "query": "retCode:-10", "value": "retCode -10 表示 用已知文件长度进行校验时失败"}
{"id": "id_4", "query": "retCode:-11", "value": "retCode -11 表示 http返回了html页面时，返回此错误码"}
{"id": "id_5", "query": "retCode:-12", "value": "retCode -12 表示 空间不足"}
{"id": "id_6", "query": "retCode:-13", "value": "retCode -13 表示 创建文件时抛出IO异常（Read-only file system），sd卡只读"}
{"id": "id_7", "query": "retCode:-14", "value": "retCode -14 表示 下载过程中保存文件时，发现文件不存在了，认为在下载过程中文件被删除，返回此错误码"}
{"id": "id_8", "query": "retCode:-15", "value": "retCode -15 表示 无网络链接导致下载失败时，返回此错误码"}
{"id": "id_9", "query": "retCode:-16", "value": "retCode -16 表示 下载抛出http相关异常，且系统返回网络已连接时，通过ping检查网络是否正常，不正常时返回此错误码，不计入失败"}
{"id": "id_10", "query": "retCode:-17", "value": "retCode -17 表示 写入下载数据时抛出IO异常(Read-only file system),sd卡只读"}
{"id": "id_11", "query": "retCode:-18", "value": "retCode -18 表示 下载指定的路径不可用"}
{"id": "id_12", "query": "retCode:-19", "value": "retCode -19 表示 下载过程中内存不足，出现OOM导致的下载中断"}
{"id": "id_13", "query": "retCode:-20", "value": "retCode -20 表示 下载自动暂停"}
{"id": "id_14", "query": "retCode:-22", "value": "retCode -22 表示 http抛出异常类错误：E_CLIENT_PROTOCOL_EXCEPTION"}
{"id": "id_15", "query": "retCode:-23", "value": "retCode -23 表示 http抛出异常类错误：E_CONNECT_TIMEOUT_EXCEPTION"}
{"id": "id_16", "query": "retCode:-24", "value": "retCode -24 表示 http抛出异常类错误：E_CONNECT_EXCEPTION"}
{"id": "id_17", "query": "retCode:-25", "value": "retCode -25 表示 http抛出异常类错误：E_SOCKET_TIMEOUT_EXCEPTION"}
{"id": "id_18", "query": "retCode:-26", "value": "retCode -26 表示 http抛出异常类错误：E_SOCKET_EXCEPTION"}
{"id": "id_19", "query": "retCode:-27", "value": "retCode -27 表示 http抛出异常类错误：E_IO_EXCEPTION"}
{"id": "id_20", "query": "retCode:-28", "value": "retCode -28 表示 http抛出异常类错误：E_EXCEPTION"}
{"id": "id_21", "query": "retCode:-29", "value": "retCode:-29 表示 http抛出异常类错误：E_UNKNOWN_HOST_EXCEPTION"}
{"id": "id_22", "query": "retCode:-30", "value": "retCode:-30 表示 http抛出异常类错误：E_HTTP_HOST_CONNECT_EXCEPTION"}
{"id": "id_23", "query": "retCode:-31", "value": "retCode:-31 表示 https握手相关错误：E_SSL_HAND_SHAKE_EXCEPTION"}
{"id": "id_24", "query": "retCode:-32", "value": "retCode -32 表示 https握手相关错误：E_SSL_HAND_KEY_EXCEPTION"}
{"id": "id_25", "query": "retCode:-33", "value": "retCode -33 表示 https握手相关错误：E_SSL_PEER_UNVERIFIED_EXCEPTION"}
{"id": "id_26", "query": "retCode:-34", "value": "retCode -34 表示 https握手相关错误：E_SSL_PROTOCOL_EXCEPTION"}
{"id": "id_27", "query": "retCode:-35", "value": "retCode -35 表示 https握手相关错误：E_SSL_EXCEPTION"}
{"id": "id_28", "query": "retCode:-36", "value": "retCode -36 表示 https握手相关错误：E_GENERAL_SECURITY_EXCEPTION"}
{"id": "id_29", "query": "retCode:-37", "value": "retCode -37 表示 https握手相关错误：E_INTERRUPTED_IO_EXCEPTION"}
{"id": "id_30", "query": "retCode:-38", "value": "retCode -38 表示 https握手相关错误：E_CERT_PATH_VALIDATOR_EXCEPTION"}
{"id": "id_31", "query": "retCode:-40", "value": "retCode -40 表示 保存文件到手机存储空间且手机存储空间不足时，返回此错误码"}
{"id": "id_32", "query": "retCode:-41", "value": "retCode -41 表示 url探测失败，http请求返回200，但是返回头中未携带Content-Range或者Content-Length字段，无法获得文件大小，则返回此错误码"}
{"id": "id_33", "query": "retCode:-42", "value": "retCode -42 表示 分段下载过程中，下载到的分段数据与当前的分段状态不符合时，返回此错误码，主要用来校验分段下载数据顺序，一般不会发生"}
{"id": "id_34", "query": "retCode:-43", "value": "retCode -43 表示 断点续传时校验每次http请求返回的文件总长度与之前探测到的文件总长度是否一致，不一致认为文件发生变化，返回此错误码"}
{"id": "id_35", "query": "retCode:-44", "value": "retCode -44 表示 断点续传时校验每次http请求返回的etag信息与之前探测到的etag信息是否一致，不一致认为文件发生变化，返回此错误码"}
{"id": "id_36", "query": "retCode:-45", "value": "retCode -45 表示 探测到文件长度，在存储空间足够的情况下，设置文件长度时抛出异常，返回此错误码，一般不会发生"}
{"id": "id_37", "query": "retCode:-46", "value": "retCode -46 表示 保存到SD卡但是SD卡不可用时，返回此错误码"}
{"id": "id_38", "query": "retCode:-47", "value": "retCode -47 表示 保存的路径不存在时，既不是SD卡，也不是手机存储空间，返回此错误码"}
{"id": "id_39", "query": "retCode:-48", "value": "retCode -48 表示 http请求及数据下载过程中抛出异常导致下载失败，返回此错误码，app可通过getFailInfo获取异常信息描述，格式为：异常类型名称|异常堆栈信息"}
{"id": "id_40", "query": "retCode:-49", "value": "retCode -49 表示 根据保存文件的路径创建新文件以及创建随机写入文件对象时抛出异常，则返回此错误码，算入失败率"}
{"id": "id_41", "query": "retCode:-50", "value": "retCode -50 表示 写入下载数据时抛出异常导致下载失败，且无法排除是否因为系统本身原因导致的，则返回此错误码，算入失败率"}
{"id": "id_42", "query": "retCode:-51", "value": "retCode -51 表示 url无效"}
{"id": "id_43", "query": "retCode:-52", "value": "retCode -52 表示 打电话导致下载失败"}
{"id": "id_44", "query": "retCode:-53", "value": "retCode -53 表示 不支持分段，一般会尝试不分段的下载请求，所以后台一般不会收到此错误码上报"}
{"id": "id_45", "query": "retCode:-54", "value": "retCode -54 表示 根据content-range头解析文件长度失败"}
{"id": "id_46", "query": "retCode:-55", "value": "retCode -55 表示 响应中无content-length头"}
{"id": "id_47", "query": "retCode:-56", "value": "retCode -56 表示 根据content-length头解析文件长度失败"}
{"id": "id_48", "query": "retCode:-57", "value": "retCode -57 表示 url跳转，一般不会上报到后台，跳转太多时上报E_Overload_Max_Redirect"}
{"id": "id_49", "query": "retCode:-58", "value": "retCode -58 表示 请求发生跳转，但无法获取新的location信息"}
{"id": "id_50", "query": "retCode:-59", "value": "retCode -59 表示 wap网关进行了长度限制返回码，在此时需要进行限制range长度后发起请求"}
{"id": "id_51", "query": "retCode:-60", "value": "retCode -60 表示 文件特征码校验失败"}
{"id": "id_52", "query": "retCode:-61", "value": "retCode -61 表示 客户端实现错误导致下载失败的情况"}
{"id": "id_53", "query": "retCode:-62", "value": "retCode -62 表示 读取数据未完成，读取某个范围的数据段时，只读取了一部分，便结束了，read返回-1"}
{"id": "id_54", "query": "retCode:-63", "value": "retCode -63 表示 FeatureReq特征码获取请求的响应头中Content-Range包含的文件总长度与已知长度不一致"}
{"id": "id_55", "query": "retCode:-64", "value": "retCode -64 表示 分段数据读取请求的响应头中Content-Range包含的文件总长度与已知长度不一致"}
{"id": "id_56", "query": "retCode:-65", "value": "retCode -65 表示 分段数据请求范围超出了服务端资源总长度"}
{"id": "id_57", "query": "retCode:-66", "value": "retCode -66 表示 网络请求被丢弃（内部错误码，一般用于暂停或者其他错误导致的强制中断）"}
{"id": "id_58", "query": "retCode:-67", "value": "retCode -67 表示 启动“直下线程”任务失败，属于内部逻辑控制错误导致的异常，正常情况不会出现"}
{"id": "id_59", "query": "retCode:-68", "value": "retCode -68 表示 断点续传时校验每次http请求返回的lastmodified字段，不一致则出现此错误码"}
{"id": "id_60", "query": "retCode:-69", "value": "retCode -69 表示 下载线程都执行完成，但是最后执行完成的下载线程错误码为OK，但是实际任务并未下载完成，一般属于内部逻辑错误"}
{"id": "id_61", "query": "retCode:-70", "value": "retCode -70 表示 http请求过程中抛出的非Exception的Throwable，上报此错误码，与-48有所区分"}
{"id": "id_62", "query": "retCode:-71", "value": "retCode -71 表示 http请求过程中抛出的异常，提示缺失网络权限，上报此错误码，与某些定制系统对网络权限的特殊限制有关"}
{"id": "id_63", "query": "retCode:-72", "value": "retCode -72 表示 重命名失败"}
{"id": "id_64", "query": "retCode:-73", "value": "retCode -73 表示 请求返回的文件size与调度得到文件size不一致"}
{"id": "id_65", "query": "retCode:-74", "value": "retCode -74 表示 不允许跳转时发生跳转行为"}
{"id": "id_66", "query": "retCode:-75", "value": "retCode -75 表示 强制校验所有校验任务都失败"}
{"id": "id_67", "query": "retCode:-76", "value": "retCode -76 表示 https的content-length与调度后台的值不一致"}
{"id": "id_68", "query": "retCode:-77", "value": "retCode -77 表示 Doze模式"}
{"id": "id_69", "query": "retCode:-78", "value": "retCode -78 表示 没有文件写入权限"}
{"id": "id_70", "query": "retCode:-80", "value": "retCode -80 表示 pcdn未开启"}
{"id": "id_71", "query": "retCode:-81", "value": "retCode -81 表示 pcdn请求失败"}
{"id": "id_72", "query": "retCode:-82", "value": "retCode -82 表示 pcdn响应失败"}
{"id": "id_73", "query": "retCode:-83", "value": "retCode -83 表示 pcdn数据写入失败"}
