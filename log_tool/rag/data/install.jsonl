{"id": "id_1", "query": "install style:0", "value": "install style:0 表示 安装类型 : 系统安装"}
{"id": "id_2", "query": "install style:1", "value": "install style:1 表示安装类型 : 永久root权限静默安装"}
{"id": "id_3", "query": "install style:2", "value": "install style:2 表示安装类型 : 临时root权限静默安装"}
{"id": "id_4", "query": "install style:3", "value": "install style:3 表示安装类型 : 省心装"}
{"id": "id_5", "query": "install style:4", "value": "install style:4 表示安装类型：系统静默安装，通过反射调用installPackage进行的静默安装<br/> 只有在应用宝为系统应用时，该类型的安装才有效"}
{"id": "id_6", "query": "install style:9", "value": "install style:9 表示安装类型： 厂商ROM漏洞安装，效果与系统静默安装一致"}
{"id": "id_7", "query": "install style:10", "value": "install style:10 表示安装类型： 第三方call起应用宝安装，效果与系统安装一致,但是要单开一个task"}
{"id": "id_8", "query": "install style:11", "value": "install style:11 表示安装类型： 第三方call起应用宝下载引起的安装，单开task处理，防止ui误导"}
{"id": "id_9", "query": "install style:12", "value": "install style:12 表示安装类型： 创建Session任务提交安装"}
{"id": "id_10", "query": "install style:13", "value": "install style:13 表示安装类型： 快速安装环境提交安装"}
{"id": "id_11", "query": "install style:14", "value": "install style:14 表示安装类型： XInstaller安装"}
{"id": "id_12", "query": "install style:-2", "value": "install style:-2 表示安装类型： 未知"}