{"id": "1", "query": "errCode:0", "value": "errCode:0 表示 正常结束，httpCode 200"}
{"id": "2", "query": "errCode:-800", "value": "errCode:-800 表示 当前没有网络"}
{"id": "3", "query": "errCode:-801", "value": "errCode:-801 表示 请求被cancel"}
{"id": "4", "query": "errCode:-802", "value": "errCode:-802 表示 请求参数错误"}
{"id": "5", "query": "errCode:-803", "value": "errCode:-803 表示 被劫持"}
{"id": "6", "query": "errCode:-804", "value": "errCode:-804 表示 jni UnsatisfiedLinkError"}
{"id": "7", "query": "errCode:-820", "value": "errCode:-820 表示 url错误"}
{"id": "8", "query": "errCode:-821", "value": "errCode:-821 表示 http协议错误"}
{"id": "9", "query": "errCode:-822", "value": "errCode:-822 表示 连接超时"}
{"id": "10", "query": "errCode:-823", "value": "errCode:-823 表示 socket超时"}
{"id": "11", "query": "errCode:-824", "value": "errCode:-824 表示 连接异常"}
{"id": "12", "query": "errCode:-825", "value": "errCode:-825 表示 socket异常"}
{"id": "13", "query": "errCode:-826", "value": "errCode:-826 表示 IO异常"}
{"id": "14", "query": "errCode:-827", "value": "errCode:-827 表示 其他Http异常"}
{"id": "15", "query": "errCode:-828", "value": "errCode:-828 表示 UNKNOWN_HOST_EXCEPTION"}
{"id": "16", "query": "errCode:-829", "value": "errCode:-829 表示 INTERRUPTED_IO_EXCEPTION"}
{"id": "17", "query": "errCode:-830", "value": "errCode:-830 表示 HTTP_HOST_CONNECT_EXCEPTION"}
{"id": "18", "query": "errCode:-831", "value": "errCode:-831 表示 SSL_HAND_SHAKE_EXCEPTION"}
{"id": "19", "query": "errCode:-832", "value": "errCode:-832 表示 SSL_HAND_KEY_EXCEPTION"}
{"id": "20", "query": "errCode:-833", "value": "errCode:-833 表示 SSL_PEER_UNVERIFIED_EXCEPTION"}
{"id": "21", "query": "errCode:-834", "value": "errCode:-834 表示 SSL_PROTOCOL_EXCEPTION"}
{"id": "22", "query": "errCode:-835", "value": "errCode:-835 表示 SSL_EXCEPTION"}
{"id": "23", "query": "errCode:-836", "value": "errCode:-836 表示 GENERAL_SECURITY_EXCEPTION"}
{"id": "24", "query": "errCode:-837", "value": "errCode:-837 表示 CERT_PATH_VALIDATOR_EXCEPTION"}
{"id": "25", "query": "errCode:-838", "value": "errCode:-838 表示 CONN_POOL_TIMEOUT_EXCEPTION"}
{"id": "26", "query": "errCode:-840", "value": "errCode:-840 表示 回包包体为空"}
{"id": "27", "query": "errCode:-841", "value": "errCode:-841 表示 没有得到Response"}
{"id": "28", "query": "errCode:-860", "value": "errCode:-860 表示 响应包错误"}
{"id": "29", "query": "errCode:-861", "value": "errCode:-861 表示 Response jce 数据包错误"}
{"id": "30", "query": "errCode:-862", "value": "errCode:-862 表示 Body jce 数据包错误"}
{"id": "31", "query": "errCode:-871", "value": "errCode:-871 表示 解压失败"}
{"id": "32", "query": "errCode:-872", "value": "errCode:-872 表示 解密失败"}
{"id": "33", "query": "errCode:-873", "value": "errCode:-873 表示 回包时上下文错误"}
{"id": "34", "query": "errCode:-874", "value": "errCode:-874 表示 身份认证失败"}
{"id": "35", "query": "errCode:-875", "value": "errCode:-875 表示 请求hold住的时间太长"}
{"id": "36", "query": "errCode:-876", "value": "errCode:-876 表示 打包请求时发生错误"}
{"id": "37", "query": "errCode:-10000", "value": "errCode:-10000 表示 打包请求时发生错误"}
{"id": "38", "query": "errCode:-926", "value": "errCode:-926 表示 长链接通道异常"}
{"id": "39", "query": "errCode:-927", "value": "errCode:-927 表示 长链接通道异常"}
{"id": "40", "query": "errCode:-940", "value": "errCode:-940 表示 回包包体为空"}
{"id": "41", "query": "errCode:-941", "value": "errCode:-941 表示 没有得到Response"}
{"id": "42", "query": "errCode:-890", "value": "errCode:-890 表示 网络服务层返回数据为空"}
{"id": "43", "query": "errCode:-891", "value": "errCode:-891 表示 网络服务层请求数据为空"}
{"id": "44", "query": "errCode:-1001", "value": "errCode:-1001 表示 查找不到相关属性"}
{"id": "45", "query": "errCode:-1002", "value": "errCode:-1002 表示 编码错误"}
{"id": "46", "query": "errCode:-1003", "value": "errCode:-1003 表示 解码错误"}
{"id": "47", "query": "errCode:-1004", "value": "errCode:-1004 表示 其他运行时错误"}
{"id": "48", "query": "errCode:-1005", "value": "errCode:-1005 表示 内存申请失败错误"}
{"id": "49", "query": "errCode:-1006", "value": "errCode:-1006 表示 可选字段不存在"}
{"id": "50", "query": "errCode:-1007", "value": "errCode:-1007 表示 伪造程序身份"}
{"id": "51", "query": "errCode:-1008", "value": "errCode:-1008 表示 证书获取失败"}
{"id": "52", "query": "errCode:-1009", "value": "errCode:-1009 表示 内存分配失败"}
{"id": "53", "query": "errCode:-1010", "value": "errCode:-1010 表示 压缩失败"}
{"id": "54", "query": "errCode:-1011", "value": "errCode:-1011 表示 加密失败"}
{"id": "55", "query": "errCode:-1012", "value": "errCode:-1012 表示 解密失败"}
{"id": "56", "query": "errCode:-1013", "value": "errCode:-1013 表示 解压失败"}
{"id": "57", "query": "errCode:-1014", "value": "errCode:-1014 表示 c层new对象失败"}
{"id": "58", "query": "errCode:-1015", "value": "errCode:-1015 表示 java层对象获取失败"}
{"id": "59", "query": "errCode:-1016", "value": "errCode:-1016 表示 伪造认证协议（随机数验证错误）"}
{"id": "60", "query": "errCode:-1017", "value": "errCode:-1017 表示 so同步失败"}
{"id": "61", "query": "errCode:-1018", "value": "errCode:-1018 表示 请求非法，在so没有动态票据的时候不加认证协议"}
{"id": "62", "query": "errCode:-1019", "value": "errCode:-1019 表示 过期废弃的请求，老的请求，但票据已经换过，不需要重换，只需重发"}
{"id": "63", "query": "errCode:-1100", "value": "errCode:-1100 表示 后台因为种种原因无法正常返回，客户端重新初始化安全通道"}
{"id": "64", "query": "errCode:-1101", "value": "errCode:-1101 表示 场景命中黑名单"}
{"id": "65", "query": "errCode:-1102", "value": "errCode:-1102 表示 功能命中黑名单"}
{"id": "66", "query": "errCode:-1103", "value": "errCode:-1103 表示 命令字命中黑名单"}
{"id": "67", "query": "errCode:-4", "value": "errCode:-4 表示 后台黑名单"}
{"id": "68", "query": "errCode:-1037", "value": "errCode:-1037 表示 AUTH降级"}
{"id": "69", "query": "errCode:-9999", "value": "errCode:-9999 表示 无效的错误码"}
