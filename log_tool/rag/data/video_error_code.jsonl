{"id": 1, "query": "error_code:11000001", "value": "error_code:11000001 表示 通用，失败。处理建议：联系开发人员"}
{"id": 2, "query": "error_code:11000010", "value": "error_code:11000010 表示 通用，未初始化。处理建议：联系开发人员"}
{"id": 3, "query": "error_code:11000011", "value": "error_code:11000011 表示 通用，未实现。处理建议：联系开发人员"}
{"id": 4, "query": "error_code:11000012", "value": "error_code:11000012 表示 通用，非法参数。处理建议：联系开发人员"}
{"id": 5, "query": "error_code:11000013", "value": "error_code:11000013 表示 通用，状态错误。处理建议：联系开发人员"}
{"id": 6, "query": "error_code:11000030", "value": "error_code:11000030 表示 通用，需要重试。处理建议：联系开发人员"}
{"id": 7, "query": "error_code:11000031", "value": "error_code:11000031 表示 通用，用户中断。处理建议：联系开发人员"}
{"id": 8, "query": "error_code:11000032", "value": "error_code:11000032 表示 通用，EOF。处理建议：联系开发人员"}
{"id": 9, "query": "error_code:11000033", "value": "error_code:11000033 表示 通用，跳过当前这次处理。处理建议：联系开发人员"}
{"id": 10, "query": "error_code:11000034", "value": "error_code:11000034 表示 通用，跳过当前数据组的处理。处理建议：联系开发人员"}
{"id": 11, "query": "error_code:11000050", "value": "error_code:11000050 表示 通用，内存不足。处理建议：检查设备内存是否充足"}
{"id": 12, "query": "error_code:11010001", "value": "error_code:11010001 表示 框架层，缓冲超时。处理建议：检查网络是否正常"}
{"id": 13, "query": "error_code:11010002", "value": "error_code:11010002 表示 框架层，prepare超时。处理建议：检查网络是否正常"}
{"id": 14, "query": "error_code:11010101", "value": "error_code:11010101 表示 框架层，data source解析失败。处理建议：检查data source是否正确"}
{"id": 15, "query": "error_code:11010102", "value": "error_code:11010102 表示 框架层，track切换超时。处理建议：检查网络是否正常"}
{"id": 16, "query": "error_code:11010103", "value": "error_code:11010103 表示 框架层，Drm private tag错误。处理建议：检查drm data source是否正确"}
{"id": 17, "query": "error_code:11010104", "value": "error_code:11010104 表示 框架层，找不到#EXT-X-KEY/ContentProtection标签。处理建议：检查drm data source是否正确"}
{"id": 18, "query": "error_code:11010105", "value": "error_code:11010105 表示 框架层，解析器类型配置有误。处理建议：检查解析器类型配置是否正确"}
{"id": 19, "query": "error_code:11010201", "value": "error_code:11010201 表示 框架层，视频解码器类型配置有误。处理建议：检查视频解码器类型配置是否正确"}
{"id": 20, "query": "error_code:11010202", "value": "error_code:11010202 表示 框架层，音频解码器类型配置有误。处理建议：检查音频解码器类型配置是否正确"}
{"id": 21, "query": "error_code:11010401", "value": "error_code:11010401 表示 框架层，视频渲染类型配置有误。处理建议：检查视频渲染类型配置是否正确"}
{"id": 22, "query": "error_code:11010402", "value": "error_code:11010402 表示 框架层，音频渲染类型配置有误。处理建议：检查音频渲染类型配置是否正确"}
{"id": 23, "query": "error_code:11020001", "value": "error_code:11020001 表示 curl，未知错误。处理建议：联系开发人员"}
{"id": 24, "query": "error_code:11020002", "value": "error_code:11020002 表示 curl，DNS解析失败。处理建议：检查网络是否正常"}
{"id": 25, "query": "error_code:11020003", "value": "error_code:11020003 表示 curl，connect失败。处理建议：检查网络是否正常"}
{"id": 26, "query": "error_code:11020004", "value": "error_code:11020004 表示 curl，connect超时。处理建议：检查网络是否正常"}
{"id": 27, "query": "error_code:11020005", "value": "error_code:11020005 表示 curl，send失败。处理建议：检查网络是否正常"}
{"id": 28, "query": "error_code:11020006", "value": "error_code:11020006 表示 curl，send超时。处理建议：检查网络是否正常"}
{"id": 29, "query": "error_code:11020007", "value": "error_code:11020007 表示 curl，recv失败。处理建议：检查网络是否正常"}
{"id": 30, "query": "error_code:11020008", "value": "error_code:11020008 表示 curl，recv超时。处理建议：检查网络是否正常"}
{"id": 31, "query": "error_code:11020101", "value": "error_code:11020101 表示 SampleAes，未知错误。处理建议：联系开发人员"}
{"id": 32, "query": "error_code:11020102", "value": "error_code:11020102 表示 SampleAes，编码不支持。处理建议：检查编码格式是否正确"}
{"id": 33, "query": "error_code:11020103", "value": "error_code:11020103 表示 SampleAes，格式不支持。处理建议：检查封装格式是否正确"}
{"id": 34, "query": "error_code:11020104", "value": "error_code:11020104 表示 SampleAes，key解析失败。处理建议：检查m3u8中是否含有EXT-X-KEY"}
{"id": 35, "query": "error_code:11020105", "value": "error_code:11020105 表示 SampleAes，音频解析失败。处理建议：联系开发人员"}
{"id": 36, "query": "error_code:11020106", "value": "error_code:11020106 表示 SampleAes，视频解析失败。处理建议：联系开发人员"}
{"id": 37, "query": "error_code:11021001", "value": "error_code:11021001 表示 Standalone，格式不支持。处理建议：检查封装格式是否正确"}
{"id": 38, "query": "error_code:11021002", "value": "error_code:11021002 表示 Standalone，无效url。处理建议：检查url是否有效"}
{"id": 39, "query": "error_code:11021003", "value": "error_code:11021003 表示 Standalone，IO错误。处理建议：检查url是否有效"}
{"id": 40, "query": "error_code:11021004", "value": "error_code:11021004 表示 Standalone，stream数据有误。处理建议：检查视频源数据是否有效"}
{"id": 41, "query": "error_code:11021005", "value": "error_code:11021005 表示 Standalone，解析m3u8失败。处理建议：检查m3u8是否有效"}
{"id": 42, "query": "error_code:11021006", "value": "error_code:11021006 表示 Standalone，hls index错误。处理建议：检查m3u8是否有效"}
{"id": 43, "query": "error_code:11021007", "value": "error_code:11021007 表示 Standalone，找不到codec param。处理建议：检查封装格式是否正确"}
{"id": 44, "query": "error_code:11021008", "value": "error_code:11021008 表示 Standalone，不支持的codec Id。处理建议：检查编码格式是否正确"}
{"id": 45, "query": "error_code:11021051", "value": "error_code:11021051 表示 Standalone TS Demuxer，stream pid非法。处理建议：检查封装格式是否正确"}
{"id": 46, "query": "error_code:11021052", "value": "error_code:11021052 表示 Standalone TS Demuxer，未知的包类型。处理建议：检查封装格式是否正确"}
{"id": 47, "query": "error_code:11021053", "value": "error_code:11021053 表示 Standalone TS Demuxer，包数据错误。处理建议：检查封装格式是否正确"}
{"id": 48, "query": "error_code:11021054", "value": "error_code:11021054 表示 Standalone TS Demuxer，找不到stream。处理建议：检查封装格式是否正确"}
{"id": 49, "query": "error_code:11021055", "value": "error_code:11021055 表示 Standalone TS Demuxer，找不到program。处理建议：检查封装格式是否正确"}
{"id": 50, "query": "error_code:11021101", "value": "error_code:11021101 表示 Standalone Dash Demuxer，非法操作。处理建议：联系开发人员"}
{"id": 51, "query": "error_code:11021102", "value": "error_code:11021102 表示 Standalone Dash Demuxer，找不到period。处理建议：联系开发人员"}
{"id": 52, "query": "error_code:11021103", "value": "error_code:11021103 表示 Standalone Dash Demuxer，解析mpd失败。处理建议：联系开发人员"}
{"id": 53, "query": "error_code:11021104", "value": "error_code:11021104 表示 Standalone Dash Demuxer，转换tp层rep失败。处理建议：联系开发人员"}
{"id": 54, "query": "error_code:11021105", "value": "error_code:11021105 表示 Standalone Dash Demuxer，读包选择rep失败。处理建议：联系开发人员"}
{"id": 55, "query": "error_code:11021106", "value": "error_code:11021106 表示 Standalone Dash Demuxer，获取下一分片失败。处理建议：联系开发人员"}
{"id": 56, "query": "error_code:11021107", "value": "error_code:11021107 表示 Standalone Dash Demuxer，无效选流。处理建议：联系开发人员"}
{"id": 57, "query": "error_code:11021108", "value": "error_code:11021108 表示 Standalone Dash Demuxer，非法媒体数组索引。处理建议：联系开发人员"}
{"id": 58, "query": "error_code:11021109", "value": "error_code:11021109 表示 Standalone Dash Demuxer，找不到sub demuxer。处理建议：联系开发人员"}
{"id": 59, "query": "error_code:11021110", "value": "error_code:11021110 表示 Standalone Dash Demuxer，非法媒体分片索引。处理建议：联系开发人员"}
{"id": 60, "query": "error_code:11021111", "value": "error_code:11021111 表示 Standalone Dash Demuxer，build tp streams失败。处理建议：联系开发人员"}
{"id": 61, "query": "error_code:11021112", "value": "error_code:11021112 表示 Standalone Dash Demuxer，找不到sub stream。处理建议：联系开发人员"}
{"id": 62, "query": "error_code:11021113", "value": "error_code:11021113 表示 Standalone Dash Demuxer，默认选流失败。处理建议：联系开发人员"}
{"id": 63, "query": "error_code:11021114", "value": "error_code:11021114 表示 Standalone Dash Demuxer，无效的流索引。处理建议：联系开发人员"}
{"id": 64, "query": "error_code:11022001", "value": "error_code:11022001 表示 FFmpeg，未知错误。处理建议：联系开发人员"}
{"id": 65, "query": "error_code:11022002", "value": "error_code:11022002 表示 FFmpeg，IO错误。处理建议：检查url是否有效"}
{"id": 66, "query": "error_code:11022003", "value": "error_code:11022003 表示 FFmpeg，数据错误。处理建议：检查视频源数据是否有效"}
{"id": 67, "query": "error_code:11022004", "value": "error_code:11022004 表示 FFmpeg，未找到decoder。处理建议：检查编码格式是否正确"}
{"id": 68, "query": "error_code:11022005", "value": "error_code:11022005 表示 FFmpeg，未找到demuxer。处理建议：检查封装格式是否正确"}
{"id": 69, "query": "error_code:11022006", "value": "error_code:11022006 表示 FFmpeg，实验性功能。处理建议：联系开发人员"}
{"id": 70, "query": "error_code:11022007", "value": "error_code:11022007 表示 FFmpeg，未找到filter。处理建议：联系开发人员"}
{"id": 71, "query": "error_code:11022008", "value": "error_code:11022008 表示 FFmpeg，未找到option。处理建议：联系开发人员"}
{"id": 72, "query": "error_code:11022009", "value": "error_code:11022009 表示 FFmpeg，未找到protocol。处理建议：检查协议是否正确"}
{"id": 73, "query": "error_code:11022010", "value": "error_code:11022010 表示 FFmpeg，未找到stream。处理建议：检查视频源数据是否有效"}
{"id": 74, "query": "error_code:11022011", "value": "error_code:11022011 表示 FFmpeg，stream错误。处理建议：检查视频源数据是否有效"}
{"id": 75, "query": "error_code:11022012", "value": "error_code:11022012 表示 FFmpeg，超时。处理建议：检查网络是否正常"}
{"id": 76, "query": "error_code:11022013", "value": "error_code:11022013 表示 FFmpeg，seek失败。处理建议：检查视频源数据是否有效"}
{"id": 77, "query": "error_code:11022101", "value": "error_code:11022101 表示 FFmpeg，网络未知错误。处理建议：检查网络是否正常"}
{"id": 78, "query": "error_code:11022102", "value": "error_code:11022102 表示 FFmpeg，DNS解析失败。处理建议：检查网络是否正常"}
{"id": 79, "query": "error_code:11022103", "value": "error_code:11022103 表示 FFmpeg，socket创建失败。处理建议：检查网络是否正常"}
{"id": 80, "query": "error_code:11022104", "value": "error_code:11022104 表示 FFmpeg，connect超时。处理建议：检查网络是否正常"}
{"id": 81, "query": "error_code:11022105", "value": "error_code:11022105 表示 FFmpeg，bind失败。处理建议：检查网络是否正常"}
{"id": 82, "query": "error_code:11022106", "value": "error_code:11022106 表示 FFmpeg，listen失败。处理建议：检查网络是否正常"}
{"id": 83, "query": "error_code:11022107", "value": "error_code:11022107 表示 FFmpeg，poll失败。处理建议：检查网络是否正常"}
{"id": 84, "query": "error_code:11022108", "value": "error_code:11022108 表示 FFmpeg，accept失败。处理建议：检查网络是否正常"}
{"id": 85, "query": "error_code:11022109", "value": "error_code:11022109 表示 FFmpeg，recv失败。处理建议：检查网络是否正常"}
{"id": 86, "query": "error_code:11022110", "value": "error_code:11022110 表示 FFmpeg，send失败。处理建议：检查网络是否正常"}
{"id": 87, "query": "error_code:11022111", "value": "error_code:11022111 表示 FFmpeg，read超时。处理建议：检查网络是否正常"}
{"id": 88, "query": "error_code:11022112", "value": "error_code:11022112 表示 FFmpeg，write超时。处理建议：检查网络是否正常"}
{"id": 89, "query": "error_code:11022113", "value": "error_code:11022113 表示 FFmpeg，http请求无效（400错误）。处理建议：http 400错误"}
{"id": 90, "query": "error_code:11022114", "value": "error_code:11022114 表示 FFmpeg，http鉴权失败（401错误）。处理建议：http 401错误"}
{"id": 91, "query": "error_code:11022115", "value": "error_code:11022115 表示 FFmpeg，http拒绝服务（403错误）。处理建议：http 403错误"}
{"id": 92, "query": "error_code:11022116", "value": "error_code:11022116 表示 FFmpeg，http未找到资源（404错误）。处理建议：http 404错误"}
{"id": 93, "query": "error_code:11022117", "value": "error_code:11022117 表示 FFmpeg，http其他4xx错误（4xx错误）。处理建议：http 4xx错误"}
{"id": 94, "query": "error_code:11022118", "value": "error_code:11022118 表示 FFmpeg，http服务器错误（5xx错误）。处理建议：http 5xx错误"}
{"id": 95, "query": "error_code:11022119", "value": "error_code:11022119 表示 FFmpeg，socket连接被拒绝。处理建议：联系开发人员"}
{"id": 96, "query": "error_code:11023001", "value": "error_code:11023001 表示 WebRTC，创建上下文失败。处理建议：联系开发人员"}
{"id": 97, "query": "error_code:11023002", "value": "error_code:11023002 表示 WebRTC，SDP交互失败。处理建议：联系开发人员"}
{"id": 98, "query": "error_code:11023003", "value": "error_code:11023003 表示 WebRTC，下载线程启动失败。处理建议：联系开发人员"}
{"id": 99, "query": "error_code:11023004", "value": "error_code:11023004 表示 WebRTC，解析codec参数超时。处理建议：联系开发人员"}
{"id": 100, "query": "error_code:11023005", "value": "error_code:11023005 表示 WebRTC，创建mini offer失败。处理建议：联系开发人员"}
{"id": 101, "query": "error_code:11023006", "value": "error_code:11023006 表示 WebRTC，对端SDP字符串解析失败。处理建议：联系开发人员"}
{"id": 102, "query": "error_code:11023007", "value": "error_code:11023007 表示 WebRTC，mini SDP交互信令服务器域名解析事变。处理建议：联系开发人员"}
{"id": 103, "query": "error_code:11023051", "value": "error_code:11023051 表示 WebRTC，ICE连接断开。处理建议：联系开发人员"}
{"id": 104, "query": "error_code:11023052", "value": "error_code:11023052 表示 WebRTC，RTC Peer连接断开。处理建议：联系开发人员"}
{"id": 105, "query": "error_code:11023201", "value": "error_code:11023201 表示 RTMP，RTMP_Init失败。处理建议：联系开发人员"}
{"id": 106, "query": "error_code:11023202", "value": "error_code:11023202 表示 RTMP，ReadPacket失败。处理建议：联系开发人员"}
{"id": 107, "query": "error_code:11023203", "value": "error_code:11023203 表示 RTMP，未接收完一个完整包。处理建议：联系开发人员"}
{"id": 108, "query": "error_code:11023204", "value": "error_code:11023204 表示 RTMP，ClientPacket失败。处理建议：联系开发人员"}
{"id": 109, "query": "error_code:11023205", "value": "error_code:11023205 表示 RTMP，m_pRtmpRequestCallbackDeleget为空，未完整初始化。处理建议：联系开发人员"}
{"id": 110, "query": "error_code:11023206", "value": "error_code:11023206 表示 RTMP，builderStreamPacket失败。处理建议：联系开发人员"}
{"id": 111, "query": "error_code:11023207", "value": "error_code:11023207 表示 RTMP，不支持的packet类型。处理建议：检查视频源是否可用"}
{"id": 112, "query": "error_code:11023208", "value": "error_code:11023208 表示 RTMP，不支持的解码器类型。处理建议：检查编码格式是否正确"}
{"id": 113, "query": "error_code:11023251", "value": "error_code:11023251 表示 RTMP，SetupURL返回失败。处理建议：检查视频源或者网络是否正常"}
{"id": 114, "query": "error_code:11023252", "value": "error_code:11023252 表示 RTMP，Connect返回失败。处理建议：检查视频源或者网络是否正常"}
{"id": 115, "query": "error_code:11023253", "value": "error_code:11023253 表示 RTMP，ConnectStreamt返回失败。处理建议：检查视频源或者网络是否正常"}
{"id": 116, "query": "error_code:11024001", "value": "error_code:11024001 表示 Standalone IO，未知错误。处理建议：检查视频源是否可用"}
{"id": 117, "query": "error_code:11024101", "value": "error_code:11024101 表示 Standalone IO File，文件权限错误。处理建议：检查文件是否有权限访问"}
{"id": 118, "query": "error_code:11024102", "value": "error_code:11024102 表示 Standalone IO File，文件打开错误。处理建议：检查文件是否可用"}
{"id": 119, "query": "error_code:11024103", "value": "error_code:11024103 表示 Standalone IO File，获取文件大小错误。处理建议：检查文件是否可用"}
{"id": 120, "query": "error_code:11024104", "value": "error_code:11024104 表示 Standalone IO File，读文件流错误。处理建议：检查文件是否可用"}
{"id": 121, "query": "error_code:11024105", "value": "error_code:11024105 表示 Standalone IO File，文件seek错误。处理建议：检查文件是否可用"}
{"id": 122, "query": "error_code:11024106", "value": "error_code:11024106 表示 Standalone IO File，文件url预处理失败。处理建议：检查文件是否可用"}
{"id": 123, "query": "error_code:11030001", "value": "error_code:11030001 表示 Native MediaCodec，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 124, "query": "error_code:11030002", "value": "error_code:11030002 表示 Native MediaCodec，不支持该codec。处理建议：检查编码格式是否正确"}
{"id": 125, "query": "error_code:11030003", "value": "error_code:11030003 表示 Native MediaCodec，解码器打开失败。处理建议：联系开发人员"}
{"id": 126, "query": "error_code:11030004", "value": "error_code:11030004 表示 Native MediaCodec，解码失败。处理建议：联系开发人员"}
{"id": 127, "query": "error_code:11030005", "value": "error_code:11030005 表示 Native MediaCodec，flush失败。处理建议：联系开发人员"}
{"id": 128, "query": "error_code:11030006", "value": "error_code:11030006 表示 Native MediaCodec，内部重启。处理建议：联系开发人员"}
{"id": 129, "query": "error_code:11030007", "value": "error_code:11030007 表示 Native MediaCodec，设置帧率或sample rate失败。处理建议：联系开发人员"}
{"id": 130, "query": "error_code:11030008", "value": "error_code:11030008 表示 Native MediaCodec，send packet超时。处理建议：联系开发人员"}
{"id": 131, "query": "error_code:11030101", "value": "error_code:11030101 表示 Java MediaCodec，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 132, "query": "error_code:11030102", "value": "error_code:11030102 表示 Java MediaCodec，不支持该codec。处理建议：检查编码格式是否正确"}
{"id": 133, "query": "error_code:11030103", "value": "error_code:11030103 表示 Java MediaCodec，解码器打开失败。处理建议：联系开发人员"}
{"id": 134, "query": "error_code:11030104", "value": "error_code:11030104 表示 Java MediaCodec，解码失败。处理建议：联系开发人员"}
{"id": 135, "query": "error_code:11030105", "value": "error_code:11030105 表示 Java MediaCodec，flush失败。处理建议：联系开发人员"}
{"id": 136, "query": "error_code:11030106", "value": "error_code:11030106 表示 Java MediaCodec，内部重启。处理建议：联系开发人员"}
{"id": 137, "query": "error_code:11030107", "value": "error_code:11030107 表示 Java MediaCodec，设置帧率或sample rate失败。处理建议：联系开发人员"}
{"id": 138, "query": "error_code:11030108", "value": "error_code:11030108 表示 Java MediaCodec，send packet超时。处理建议：联系开发人员"}
{"id": 139, "query": "error_code:11030109", "value": "error_code:11030109 表示 Java MediaCodec，open超时。处理建议：联系开发人员"}
{"id": 140, "query": "error_code:11030201", "value": "error_code:11030201 表示 VideoToolbox，该设备不支持VideoToolbox。处理建议：检查设备系统版本是否大于等于8.0"}
{"id": 141, "query": "error_code:11030202", "value": "error_code:11030202 表示 VideoToolbox，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 142, "query": "error_code:11030203", "value": "error_code:11030203 表示 VideoToolbox，不支持该codec。处理建议：检查编码格式是否正确"}
{"id": 143, "query": "error_code:11030204", "value": "error_code:11030204 表示 VideoToolbox，创建session失败。处理建议：联系开发人员"}
{"id": 144, "query": "error_code:11030205", "value": "error_code:11030205 表示 VideoToolbox，创建FormatDescription失败。处理建议：联系开发人员"}
{"id": 145, "query": "error_code:11030206", "value": "error_code:11030206 表示 VideoToolbox，创建BlockBuffer失败。处理建议：联系开发人员"}
{"id": 146, "query": "error_code:11030207", "value": "error_code:11030207 表示 VideoToolbox，创建SampleBuffer失败。处理建议：联系开发人员"}
{"id": 147, "query": "error_code:11030208", "value": "error_code:11030208 表示 VideoToolbox，解码失败。处理建议：联系开发人员"}
{"id": 148, "query": "error_code:11030209", "value": "error_code:11030209 表示 VideoToolbox，flush失败。处理建议：联系开发人员"}
{"id": 149, "query": "error_code:11030210", "value": "error_code:11030210 表示 VideoToolbox，session失效。处理建议：联系开发人员"}
{"id": 150, "query": "error_code:11030301", "value": "error_code:11030301 表示 AudioToolbox，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 151, "query": "error_code:11030302", "value": "error_code:11030302 表示 AudioToolbox，不支持该codec。处理建议：检查编码格式是否正确"}
{"id": 152, "query": "error_code:11030303", "value": "error_code:11030303 表示 AudioToolbox，获取MagicCookie失败。处理建议：联系开发人员"}
{"id": 153, "query": "error_code:11030304", "value": "error_code:11030304 表示 AudioToolbox，获取FormatInfo失败。处理建议：联系开发人员"}
{"id": 154, "query": "error_code:11030305", "value": "error_code:11030305 表示 AudioToolbox，创建AudioConverter失败。处理建议：联系开发人员"}
{"id": 155, "query": "error_code:11030306", "value": "error_code:11030306 表示 AudioToolbox，设置MagicCookie失败。处理建议：联系开发人员"}
{"id": 156, "query": "error_code:11030307", "value": "error_code:11030307 表示 AudioToolbox，解码器打开失败。处理建议：联系开发人员"}
{"id": 157, "query": "error_code:11030308", "value": "error_code:11030308 表示 AudioToolbox，解码失败。处理建议：联系开发人员"}
{"id": 158, "query": "error_code:11030309", "value": "error_code:11030309 表示 AudioToolbox，flush失败。处理建议：联系开发人员"}
{"id": 159, "query": "error_code:11030401", "value": "error_code:11030401 表示 FFmpeg，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 160, "query": "error_code:11030402", "value": "error_code:11030402 表示 FFmpeg，不支持该codec。处理建议：检查编码格式是否正确"}
{"id": 161, "query": "error_code:11030403", "value": "error_code:11030403 表示 FFmpeg，codecpar转换失败。处理建议：检查编码格式是否正确"}
{"id": 162, "query": "error_code:11030404", "value": "error_code:11030404 表示 FFmpeg，获取context失败。处理建议：检查编码格式是否正确"}
{"id": 163, "query": "error_code:11030405", "value": "error_code:11030405 表示 FFmpeg，解码器打开失败。处理建议：联系开发人员"}
{"id": 164, "query": "error_code:11030406", "value": "error_code:11030406 表示 FFmpeg，send packet失败。处理建议：联系开发人员"}
{"id": 165, "query": "error_code:11030407", "value": "error_code:11030407 表示 FFmpeg，receive frame失败。处理建议：联系开发人员"}
{"id": 166, "query": "error_code:11030408", "value": "error_code:11030408 表示 FFmpeg，flush失败。处理建议：联系开发人员"}
{"id": 167, "query": "error_code:11030501", "value": "error_code:11030501 表示 Dolby，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 168, "query": "error_code:11030502", "value": "error_code:11030502 表示 Dolby，query info失败。处理建议：联系开发人员"}
{"id": 169, "query": "error_code:11030503", "value": "error_code:11030503 表示 Dolby，query memory失败。处理建议：联系开发人员"}
{"id": 170, "query": "error_code:11030504", "value": "error_code:11030504 表示 Dolby，解码器打开失败。处理建议：联系开发人员"}
{"id": 171, "query": "error_code:11030505", "value": "error_code:11030505 表示 Dolby，不支持该输出channels个数。处理建议：联系开发人员"}
{"id": 172, "query": "error_code:11030506", "value": "error_code:11030506 表示 Dolby，不支持该输出格式。处理建议：联系开发人员"}
{"id": 173, "query": "error_code:11030507", "value": "error_code:11030507 表示 Dolby，输入数据长度超限。处理建议：联系开发人员"}
{"id": 174, "query": "error_code:11030508", "value": "error_code:11030508 表示 Dolby，解码失败。处理建议：联系开发人员"}
{"id": 175, "query": "error_code:11030509", "value": "error_code:11030509 表示 Dolby，flush失败。处理建议：联系开发人员"}
{"id": 176, "query": "error_code:11030601", "value": "error_code:11030601 表示 Standalone，codec参数非法。处理建议：检查编码格式是否正确"}
{"id": 177, "query": "error_code:11030602", "value": "error_code:11030602 表示 Standalone，不支持该codec。处理建议：检查编码格式是否正确"}
{"id": 178, "query": "error_code:11030603", "value": "error_code:11030603 表示 Standalone，解码器打开失败。处理建议：联系开发人员"}
{"id": 179, "query": "error_code:11030604", "value": "error_code:11030604 表示 Standalone，解码失败。处理建议：联系开发人员"}
{"id": 180, "query": "error_code:11030605", "value": "error_code:11030605 表示 Standalone，flush失败。处理建议：联系开发人员"}
{"id": 181, "query": "error_code:11060001", "value": "error_code:11060001 表示 AudioTrack，参数非法。处理建议：检查音频参数信息是否正确"}
{"id": 182, "query": "error_code:11060002", "value": "error_code:11060002 表示 AudioTrack，init失败。处理建议：检查音频参数信息是否正确"}
{"id": 183, "query": "error_code:11060003", "value": "error_code:11060003 表示 AudioTrack，play失败。处理建议：联系开发人员"}
{"id": 184, "query": "error_code:11060004", "value": "error_code:11060004 表示 AudioTrack，pause失败。处理建议：联系开发人员"}
{"id": 185, "query": "error_code:11060005", "value": "error_code:11060005 表示 AudioTrack，render失败。处理建议：联系开发人员"}
{"id": 186, "query": "error_code:11060006", "value": "error_code:11060006 表示 AudioTrack，render超时。处理建议：联系开发人员"}
{"id": 187, "query": "error_code:11060007", "value": "error_code:11060007 表示 AudioTrack，flush失败。处理建议：联系开发人员"}
{"id": 188, "query": "error_code:11060008", "value": "error_code:11060008 表示 AudioTrack，设置音量失败。处理建议：联系开发人员"}
{"id": 189, "query": "error_code:11060009", "value": "error_code:11060009 表示 AudioTrack，设置静音失败。处理建议：联系开发人员"}
{"id": 190, "query": "error_code:11060010", "value": "error_code:11060010 表示 AudioTrack，DEAD_OBJECT，需要重新创建。处理建议：联系开发人员"}
{"id": 191, "query": "error_code:11060201", "value": "error_code:11060201 表示 Oboe，参数非法。处理建议：检查音频参数信息是否正确"}
{"id": 192, "query": "error_code:11060202", "value": "error_code:11060202 表示 Oboe，init失败。处理建议：检查音频参数信息是否正确"}
{"id": 193, "query": "error_code:11060203", "value": "error_code:11060203 表示 Oboe，play失败。处理建议：联系开发人员"}
{"id": 194, "query": "error_code:11060204", "value": "error_code:11060204 表示 Oboe，pause失败。处理建议：联系开发人员"}
{"id": 195, "query": "error_code:11060205", "value": "error_code:11060205 表示 Oboe，render失败。处理建议：联系开发人员"}
{"id": 196, "query": "error_code:11060206", "value": "error_code:11060206 表示 Oboe，render超时。处理建议：联系开发人员"}
{"id": 197, "query": "error_code:11060207", "value": "error_code:11060207 表示 Oboe，flush失败。处理建议：联系开发人员"}
{"id": 198, "query": "error_code:11060208", "value": "error_code:11060208 表示 Oboe，设置音量失败。处理建议：联系开发人员"}
{"id": 199, "query": "error_code:11060209", "value": "error_code:11060209 表示 Oboe，设置静音失败。处理建议：联系开发人员"}
{"id": 200, "query": "error_code:11060301", "value": "error_code:11060301 表示 OpenSL，参数非法。处理建议：检查音频参数信息是否正确"}
{"id": 201, "query": "error_code:11060302", "value": "error_code:11060302 表示 OpenSL，init失败。处理建议：检查音频参数信息是否正确"}
{"id": 202, "query": "error_code:11060303", "value": "error_code:11060303 表示 OpenSL，play失败。处理建议：联系开发人员"}
{"id": 203, "query": "error_code:11060304", "value": "error_code:11060304 表示 OpenSL，pause失败。处理建议：联系开发人员"}
{"id": 204, "query": "error_code:11060305", "value": "error_code:11060305 表示 OpenSL，render失败。处理建议：联系开发人员"}
{"id": 205, "query": "error_code:11060306", "value": "error_code:11060306 表示 OpenSL，render超时。处理建议：联系开发人员"}
{"id": 206, "query": "error_code:11060307", "value": "error_code:11060307 表示 OpenSL，flush失败。处理建议：联系开发人员"}
{"id": 207, "query": "error_code:11060308", "value": "error_code:11060308 表示 OpenSL，设置音量失败。处理建议：联系开发人员"}
{"id": 208, "query": "error_code:11060309", "value": "error_code:11060309 表示 OpenSL，设置静音失败。处理建议：联系开发人员"}
{"id": 209, "query": "error_code:11060501", "value": "error_code:11060501 表示 AudioQueue，参数非法。处理建议：检查音频参数信息是否正确"}
{"id": 210, "query": "error_code:11060502", "value": "error_code:11060502 表示 AudioQueue，init失败。处理建议：检查音频参数信息是否正确"}
{"id": 211, "query": "error_code:11060503", "value": "error_code:11060503 表示 AudioQueue，play失败。处理建议：联系开发人员"}
{"id": 212, "query": "error_code:11060504", "value": "error_code:11060504 表示 AudioQueue，pause失败。处理建议：联系开发人员"}
{"id": 213, "query": "error_code:11060505", "value": "error_code:11060505 表示 AudioQueue，render失败。处理建议：联系开发人员"}
{"id": 214, "query": "error_code:11060506", "value": "error_code:11060506 表示 AudioQueue，render超时。处理建议：联系开发人员"}
{"id": 215, "query": "error_code:11060507", "value": "error_code:11060507 表示 AudioQueue，flush失败。处理建议：联系开发人员"}
{"id": 216, "query": "error_code:11060508", "value": "error_code:11060508 表示 AudioQueue，设置音量失败。处理建议：联系开发人员"}
{"id": 217, "query": "error_code:11060509", "value": "error_code:11060509 表示 AudioQueue，设置静音失败。处理建议：联系开发人员"}
{"id": 218, "query": "error_code:11060701", "value": "error_code:11060701 表示 DSound，初始化失败。处理建议：联系开发人员"}
{"id": 219, "query": "error_code:11060702", "value": "error_code:11060702 表示 DSound，格式不支持。处理建议：联系开发人员"}
{"id": 220, "query": "error_code:11060703", "value": "error_code:11060703 表示 DSound，buffer丢失。处理建议：联系开发人员"}
{"id": 221, "query": "error_code:11060704", "value": "error_code:11060704 表示 DSound，播放过程中失败。处理建议：联系开发人员"}
{"id": 222, "query": "error_code:11060705", "value": "error_code:11060705 表示 DSound，参数错误。处理建议：联系开发人员"}
{"id": 223, "query": "error_code:11060706", "value": "error_code:11060706 表示 DSound，通用的失败例如指针为空。处理建议：联系开发人员"}
{"id": 224, "query": "error_code:11060707", "value": "error_code:11060707 表示 DSound，render超时错误。处理建议：联系开发人员"}
{"id": 225, "query": "error_code:11060708", "value": "error_code:11060708 表示 DSound，render状态不对。处理建议：联系开发人员"}
{"id": 226, "query": "error_code:11060801", "value": "error_code:11060801 表示 Wasapi，初始化失败。处理建议：联系开发人员"}
{"id": 227, "query": "error_code:11060802", "value": "error_code:11060802 表示 Wasapi，格式不支持。处理建议：联系开发人员"}
{"id": 228, "query": "error_code:11060803", "value": "error_code:11060803 表示 Wasapi，buffer丢失。处理建议：联系开发人员"}
{"id": 229, "query": "error_code:11060804", "value": "error_code:11060804 表示 Wasapi，播放过程中失败。处理建议：联系开发人员"}
{"id": 230, "query": "error_code:11060805", "value": "error_code:11060805 表示 Wasapi，参数错误。处理建议：联系开发人员"}
{"id": 231, "query": "error_code:11060806", "value": "error_code:11060806 表示 Wasapi，通用的失败例如指针为空。处理建议：联系开发人员"}
{"id": 232, "query": "error_code:11060807", "value": "error_code:11060807 表示 Wasapi，render超时错误。处理建议：联系开发人员"}
{"id": 233, "query": "error_code:11060901", "value": "error_code:11060901 表示 Waveout，初始化失败。处理建议：联系开发人员"}
{"id": 234, "query": "error_code:11060902", "value": "error_code:11060902 表示 Waveout，格式不支持。处理建议：联系开发人员"}
{"id": 235, "query": "error_code:11060903", "value": "error_code:11060903 表示 Waveout，buffer丢失。处理建议：联系开发人员"}
{"id": 236, "query": "error_code:11060904", "value": "error_code:11060904 表示 Waveout，播放过程中失败。处理建议：联系开发人员"}
{"id": 237, "query": "error_code:11060905", "value": "error_code:11060905 表示 Waveout，参数错误。处理建议：联系开发人员"}
{"id": 238, "query": "error_code:11060906", "value": "error_code:11060906 表示 Waveout，通用的失败例如指针为空。处理建议：联系开发人员"}
{"id": 239, "query": "error_code:11060907", "value": "error_code:11060907 表示 Waveout，render超时错误。处理建议：联系开发人员"}
{"id": 240, "query": "error_code:11064001", "value": "error_code:11064001 表示 OpenGL，参数非法。处理建议：检查视频参数信息是否正确"}
{"id": 241, "query": "error_code:11064002", "value": "error_code:11064002 表示 OpenGL，设置target失败。处理建议：联系开发人员"}
{"id": 242, "query": "error_code:11064003", "value": "error_code:11064003 表示 OpenGL，设置gravity失败。处理建议：联系开发人员"}
{"id": 243, "query": "error_code:11064004", "value": "error_code:11064004 表示 OpenGL，init失败。处理建议：检查视频参数信息是否正确"}
{"id": 244, "query": "error_code:11064005", "value": "error_code:11064005 表示 OpenGL，render失败。处理建议：联系开发人员"}
{"id": 245, "query": "error_code:11064006", "value": "error_code:11064006 表示 OpenGL，render超时。处理建议：联系开发人员"}
{"id": 246, "query": "error_code:11064101", "value": "error_code:11064101 表示 Metal，参数非法。处理建议：检查视频参数信息是否正确"}
{"id": 247, "query": "error_code:11064102", "value": "error_code:11064102 表示 Metal，设置target失败。处理建议：联系开发人员"}
{"id": 248, "query": "error_code:11064103", "value": "error_code:11064103 表示 Metal，设置gravity失败。处理建议：联系开发人员"}
{"id": 249, "query": "error_code:11064104", "value": "error_code:11064104 表示 Metal，init失败。处理建议：检查视频参数信息是否正确"}
{"id": 250, "query": "error_code:11064105", "value": "error_code:11064105 表示 Metal，render失败。处理建议：联系开发人员"}
{"id": 251, "query": "error_code:11064106", "value": "error_code:11064106 表示 Metal，render超时。处理建议：联系开发人员"}
{"id": 252, "query": "error_code:11064301", "value": "error_code:11064301 表示 D3D11，设备丢失。处理建议：联系开发人员"}
{"id": 253, "query": "error_code:11064302", "value": "error_code:11064302 表示 D3D11，设备为空。处理建议：联系开发人员"}
{"id": 254, "query": "error_code:11064303", "value": "error_code:11064303 表示 D3D11，filter prepare失败。处理建议：联系开发人员"}
{"id": 255, "query": "error_code:11064304", "value": "error_code:11064304 表示 D3D11，layer frame到纹理失败。处理建议：联系开发人员"}
{"id": 256, "query": "error_code:11064305", "value": "error_code:11064305 表示 D3D11，layer tex为空。处理建议：联系开发人员"}
{"id": 257, "query": "error_code:11064306", "value": "error_code:11064306 表示 D3D11，layer 顶点缓冲失败。处理建议：联系开发人员"}
{"id": 258, "query": "error_code:11064307", "value": "error_code:11064307 表示 D3D11，layer 顶点着色器失败。处理建议：联系开发人员"}
{"id": 259, "query": "error_code:11064308", "value": "error_code:11064308 表示 D3D11，layer 像素着色器失败。处理建议：联系开发人员"}
{"id": 260, "query": "error_code:11064309", "value": "error_code:11064309 表示 D3D11，创建交换链路失败。处理建议：联系开发人员"}
{"id": 261, "query": "error_code:11064310", "value": "error_code:11064310 表示 D3D11，Check ALPHA失败。处理建议：联系开发人员"}
{"id": 262, "query": "error_code:11064311", "value": "error_code:11064311 表示 D3D11，present失败。处理建议：联系开发人员"}
{"id": 263, "query": "error_code:11064312", "value": "error_code:11064312 表示 D3D11，target view 为空。处理建议：联系开发人员"}
{"id": 264, "query": "error_code:11064401", "value": "error_code:11064401 表示 D3D9，设备为空。处理建议：联系开发人员"}
{"id": 265, "query": "error_code:11064402", "value": "error_code:11064402 表示 D3D9，filter prepare失败。处理建议：联系开发人员"}
{"id": 266, "query": "error_code:11064403", "value": "error_code:11064403 表示 D3D9，filter draw失败。处理建议：联系开发人员"}
{"id": 267, "query": "error_code:11064404", "value": "error_code:11064404 表示 D3D9，layer frame到纹理失败。处理建议：联系开发人员"}
{"id": 268, "query": "error_code:11064405", "value": "error_code:11064405 表示 D3D9，layer tex为空。处理建议：联系开发人员"}
{"id": 269, "query": "error_code:11064406", "value": "error_code:11064406 表示 D3D9，layer顶点缓冲失败。处理建议：联系开发人员"}
{"id": 270, "query": "error_code:11064407", "value": "error_code:11064407 表示 D3D9 layer draw失败。处理建议：联系开发人员"}
{"id": 271, "query": "error_code:11064408", "value": "error_code:11064408 表示 D3D9，创建交换链路失败。处理建议：联系开发人员"}
{"id": 272, "query": "error_code:11064409", "value": "error_code:11064409 表示 D3D9，获取接口失败。处理建议：联系开发人员"}
{"id": 273, "query": "error_code:11064410", "value": "error_code:11064410 表示 D3D9，创建共享纹理失败。处理建议：联系开发人员"}
{"id": 274, "query": "error_code:11064411", "value": "error_code:11064411 表示 D3D9，BeginScene失败。处理建议：联系开发人员"}
{"id": 275, "query": "error_code:11064412", "value": "error_code:11064412 表示 D3D9，获取后台缓冲失败。处理建议：联系开发人员"}
{"id": 276, "query": "error_code:11064413", "value": "error_code:11064413 表示 D3D9，present失败。处理建议：联系开发人员"}
{"id": 277, "query": "error_code:11064501", "value": "error_code:11064501 表示 GDI，filter prepare失败。处理建议：联系开发人员"}
{"id": 278, "query": "error_code:11064502", "value": "error_code:11064502 表示 GDI，layer tex为空。处理建议：联系开发人员"}
{"id": 279, "query": "error_code:11064503", "value": "error_code:11064503 表示 GDI layer draw失败。处理建议：联系开发人员"}
{"id": 280, "query": "error_code:11064504", "value": "error_code:11064504 表示 GDI，layer 格式不支持。处理建议：联系开发人员"}
{"id": 281, "query": "error_code:11064505", "value": "error_code:11064505 表示 GDI，创建纹理不支持。处理建议：联系开发人员"}
{"id": 282, "query": "error_code:11070001", "value": "error_code:11070001 表示 Java MediaDrm，非法参数。处理建议：联系开发人员"}
{"id": 283, "query": "error_code:11070002", "value": "error_code:11070002 表示 Java MediaDrm，不支持Drm。处理建议：联系开发人员"}
{"id": 284, "query": "error_code:11070003", "value": "error_code:11070003 表示 Java MediaDrm，设备唯一证书请求，非法参数。处理建议：联系开发人员"}
{"id": 285, "query": "error_code:11070004", "value": "error_code:11070004 表示 Java MediaDrm，设备唯一证书请求，接口异常。处理建议：联系开发人员"}
{"id": 286, "query": "error_code:11070005", "value": "error_code:11070005 表示 Java MediaDrm，设备唯一证书请求，申请url内存失败，内存不足。处理建议：联系开发人员"}
{"id": 287, "query": "error_code:11070006", "value": "error_code:11070006 表示 Java MediaDrm，设备唯一证书请求，非法请求参数。处理建议：联系开发人员"}
{"id": 288, "query": "error_code:11070007", "value": "error_code:11070007 表示 Java MediaDrm，设备唯一证书请求，申请请求数据内存失败，内存不足。处理建议：联系开发人员"}
{"id": 289, "query": "error_code:11070008", "value": "error_code:11070008 表示 Java MediaDrm，设备唯一证书请求返回，非法参数。处理建议：联系开发人员"}
{"id": 290, "query": "error_code:11070009", "value": "error_code:11070009 表示 Java MediaDrm，设备唯一证书请求返回，接口异常。处理建议：联系开发人员"}
{"id": 291, "query": "error_code:11070010", "value": "error_code:11070010 表示 Java MediaDrm，设备唯一证书请求返回，服务端拒绝。处理建议：联系开发人员"}
{"id": 292, "query": "error_code:11070011", "value": "error_code:11070011 表示 Java MediaDrm，密钥请求，非法参数。处理建议：联系开发人员"}
{"id": 293, "query": "error_code:11070012", "value": "error_code:11070012 表示 Java MediaDrm，密钥请求，接口异常。处理建议：联系开发人员"}
{"id": 294, "query": "error_code:11070013", "value": "error_code:11070013 表示 Java MediaDrm，密钥请求，非法请求参数。处理建议：联系开发人员"}
{"id": 295, "query": "error_code:11070014", "value": "error_code:11070014 表示 Java MediaDrm，密钥请求返回，非法参数。处理建议：联系开发人员"}
{"id": 296, "query": "error_code:11070015", "value": "error_code:11070015 表示 Java MediaDrm，密钥请求返回，接口异常。处理建议：联系开发人员"}
{"id": 297, "query": "error_code:11070016", "value": "error_code:11070016 表示 Java MediaDrm，密钥请求返回，服务端拒绝。处理建议：联系开发人员"}
{"id": 298, "query": "error_code:11070017", "value": "error_code:11070017 表示 Java MediaDrm，打开Drm会话，非法参数。处理建议：联系开发人员"}
{"id": 299, "query": "error_code:11070018", "value": "error_code:11070018 表示 Java MediaDrm，打开Drm会话，异常。处理建议：联系开发人员"}
{"id": 300, "query": "error_code:11070019", "value": "error_code:11070019 表示 Java MediaDrm，打开Drm会话，会话资源繁忙。处理建议：联系开发人员"}
{"id": 301, "query": "error_code:11070020", "value": "error_code:11070020 表示 Java MediaDrm，打开Drm会话，会话ID返回空。处理建议：联系开发人员"}
{"id": 302, "query": "error_code:11070021", "value": "error_code:11070021 表示 Java MediaDrm，打开Drm会话，会话ID长度非法。处理建议：联系开发人员"}
{"id": 303, "query": "error_code:11070022", "value": "error_code:11070022 表示 Java MediaDrm，打开Drm会话，申请会话ID内存失败，内存不足。处理建议：联系开发人员"}
{"id": 304, "query": "error_code:11070023", "value": "error_code:11070023 表示 Java MediaDrm，关闭Drm会话，非法参数。处理建议：联系开发人员"}
{"id": 305, "query": "error_code:11070024", "value": "error_code:11070024 表示 Java MediaDrm，关闭Drm会话，会话未打开。处理建议：联系开发人员"}
{"id": 306, "query": "error_code:11070025", "value": "error_code:11070025 表示 Java MediaDrm，关闭Drm会话，接口异常。处理建议：联系开发人员"}
{"id": 307, "query": "error_code:11070026", "value": "error_code:11070026 表示 Java MediaDrm，获取解密对象，非法参数。处理建议：联系开发人员"}
{"id": 308, "query": "error_code:11070027", "value": "error_code:11070027 表示 Java MediaDrm，获取解密对象，会话未打开。处理建议：联系开发人员"}
{"id": 309, "query": "error_code:11070028", "value": "error_code:11070028 表示 Java MediaDrm，获取解密对象，接口异常。处理建议：联系开发人员"}
{"id": 310, "query": "error_code:11070029", "value": "error_code:11070029 表示 Java MediaDrm，Drm资源释放，接口异常。处理建议：联系开发人员"}
{"id": 311, "query": "error_code:11070030", "value": "error_code:11070030 表示 Java MediaDrm，JNI获取环境变量异常。处理建议：联系开发人员"}
{"id": 312, "query": "error_code:11070031", "value": "error_code:11070031 表示 Java MediaDrm，需要配置证书。处理建议：联系开发人员"}
{"id": 313, "query": "error_code:11070032", "value": "error_code:11070032 表示 Java MediaDrm，创建对象方法，非法参数。处理建议：联系开发人员"}
{"id": 314, "query": "error_code:11070033", "value": "error_code:11070033 表示 Java MediaDrm，创建对象方法，内存不够。处理建议：联系开发人员"}
{"id": 315, "query": "error_code:11070034", "value": "error_code:11070034 表示 Java MediaDrm，创建对象方法，JNI接口异常。处理建议：联系开发人员"}
{"id": 316, "query": "error_code:11070035", "value": "error_code:11070035 表示 Java MediaDrm，创建对象方法，参数非法。处理建议：联系开发人员"}
{"id": 317, "query": "error_code:11070036", "value": "error_code:11070036 表示 Java MediaDrm，创建对象方法，未知错误。处理建议：联系开发人员"}
{"id": 318, "query": "error_code:11070037", "value": "error_code:11070037 表示 Java MediaDrm，创建对象方法，API level不支持。处理建议：联系开发人员"}
{"id": 319, "query": "error_code:11070038", "value": "error_code:11070038 表示 Java MediaDrm，创建对象方法，创建失败。处理建议：联系开发人员"}
{"id": 320, "query": "error_code:11070039", "value": "error_code:11070039 表示 Java MediaDrm，创建Drm代理未初始化。处理建议：联系开发人员"}
{"id": 321, "query": "error_code:11070201", "value": "error_code:11070201 表示 Drm网络错误，设备唯一证书网络请求失败。处理建议：联系开发人员"}
{"id": 322, "query": "error_code:11070202", "value": "error_code:11070202 表示 Drm网络错误，设备唯一证书网络请求，返回错误状态码。处理建议：联系开发人员"}
{"id": 323, "query": "error_code:11070203", "value": "error_code:11070203 表示 Drm网络错误，密钥网络请求失败。处理建议：联系开发人员"}
{"id": 324, "query": "error_code:11070204", "value": "error_code:11070204 表示 Drm网络错误，密钥网络请求，返回错误状态码。处理建议：联系开发人员"}
{"id": 325, "query": "error_code:11070205", "value": "error_code:11070205 表示 Drm网络错误，密钥解析失败。处理建议：联系开发人员"}
{"id": 326, "query": "error_code:11070301", "value": "error_code:11070301 表示 Drm会话预备，状态错误。处理建议：联系开发人员"}
{"id": 327, "query": "error_code:11070302", "value": "error_code:11070302 表示 Drm会话预备，线程创建错误。处理建议：联系开发人员"}
{"id": 328, "query": "error_code:11070303", "value": "error_code:11070303 表示 Drm会话获取解密对象，状态错误。处理建议：联系开发人员"}
{"id": 329, "query": "error_code:11070401", "value": "error_code:11070401 表示 Drm密钥请求，解析#EXT-X-KEY出错。处理建议：联系开发人员"}
{"id": 330, "query": "error_code:11070402", "value": "error_code:11070402 表示 Drm密钥请求，加密方式不支持。处理建议：联系开发人员"}
{"id": 331, "query": "error_code:11070403", "value": "error_code:11070403 表示 Drm密钥请求，申请会话ID内存失败，内存不足。处理建议：联系开发人员"}
{"id": 332, "query": "error_code:11070404", "value": "error_code:11070404 表示 Drm密钥请求返回，解析失败。处理建议：联系开发人员"}
{"id": 333, "query": "error_code:11071001", "value": "error_code:11071001 表示 ChinaDrm Module，非法状态。处理建议：联系开发人员"}
{"id": 334, "query": "error_code:11071002", "value": "error_code:11071002 表示 ChinaDrm Module，非法状态。处理建议：联系开发人员"}
{"id": 335, "query": "error_code:11071003", "value": "error_code:11071003 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 336, "query": "error_code:11071004", "value": "error_code:11071004 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 337, "query": "error_code:11071005", "value": "error_code:11071005 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 338, "query": "error_code:11071006", "value": "error_code:11071006 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 339, "query": "error_code:11071007", "value": "error_code:11071007 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 340, "query": "error_code:11071008", "value": "error_code:11071008 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 341, "query": "error_code:11071009", "value": "error_code:11071009 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 342, "query": "error_code:11071010", "value": "error_code:11071010 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 343, "query": "error_code:11071011", "value": "error_code:11071011 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 344, "query": "error_code:11071012", "value": "error_code:11071012 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 345, "query": "error_code:11071013", "value": "error_code:11071013 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 346, "query": "error_code:11071014", "value": "error_code:11071014 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 347, "query": "error_code:11071015", "value": "error_code:11071015 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 348, "query": "error_code:11071016", "value": "error_code:11071016 表示 ChinaDrm Module，非法参数。处理建议：联系开发人员"}
{"id": 349, "query": "error_code:11071017", "value": "error_code:11071017 表示 ChinaDrm Module，需要配置provision。处理建议：联系开发人员"}
{"id": 350, "query": "error_code:11071018", "value": "error_code:11071018 表示 ChinaDrm Module，会话未打开。处理建议：联系开发人员"}
{"id": 351, "query": "error_code:11071019", "value": "error_code:11071019 表示 ChinaDrm Module，会话未打开。处理建议：联系开发人员"}
{"id": 352, "query": "error_code:11071020", "value": "error_code:11071020 表示 ChinaDrm Module，会话未打开。处理建议：联系开发人员"}
{"id": 353, "query": "error_code:11071021", "value": "error_code:11071021 表示 ChinaDrm Module，会话未打开。处理建议：联系开发人员"}
{"id": 354, "query": "error_code:11071101", "value": "error_code:11071101 表示 ChinaDrm Session，非法状态。处理建议：联系开发人员"}
{"id": 355, "query": "error_code:11071102", "value": "error_code:11071102 表示 ChinaDrm Session，非法状态。处理建议：联系开发人员"}
{"id": 356, "query": "error_code:11071103", "value": "error_code:11071103 表示 ChinaDrm Session，非法状态。处理建议：联系开发人员"}
{"id": 357, "query": "error_code:11071104", "value": "error_code:11071104 表示 ChinaDrm Session，加密算法不支持。处理建议：联系开发人员"}
{"id": 358, "query": "error_code:11071105", "value": "error_code:11071105 表示 ChinaDrm Session，加密算法不支持。处理建议：联系开发人员"}
{"id": 359, "query": "error_code:11071106", "value": "error_code:11071106 表示 ChinaDrm Session，解密密钥不在许可证列表中。处理建议：联系开发人员"}
{"id": 360, "query": "error_code:11071107", "value": "error_code:11071107 表示 ChinaDrm Session，解密会话不在许可证中。处理建议：联系开发人员"}
{"id": 361, "query": "error_code:11071201", "value": "error_code:11071201 表示 ChinaDrm License，生成随机数出错。处理建议：联系开发人员"}
{"id": 362, "query": "error_code:11071202", "value": "error_code:11071202 表示 ChinaDrm License，许可证参数校验错误。处理建议：联系开发人员"}
{"id": 363, "query": "error_code:11071203", "value": "error_code:11071203 表示 ChinaDrm License，服务器证书不存在。处理建议：联系开发人员"}
{"id": 364, "query": "error_code:11071204", "value": "error_code:11071204 表示 ChinaDrm License，服务器证书校验错误。处理建议：联系开发人员"}
{"id": 365, "query": "error_code:11071205", "value": "error_code:11071205 表示 ChinaDrm License，服务器证书不在许可证列表中。处理建议：联系开发人员"}
{"id": 366, "query": "error_code:11071206", "value": "error_code:11071206 表示 ChinaDrm License，许可证未找到。处理建议：联系开发人员"}
{"id": 367, "query": "error_code:11071207", "value": "error_code:11071207 表示 ChinaDrm License，HMAC校验错误。处理建议：联系开发人员"}
{"id": 368, "query": "error_code:11071208", "value": "error_code:11071208 表示 ChinaDrm License，初始化数据解析错误。处理建议：联系开发人员"}
{"id": 369, "query": "error_code:11071209", "value": "error_code:11071209 表示 ChinaDrm License，初始化数据解析错误。处理建议：联系开发人员"}
{"id": 370, "query": "error_code:11071210", "value": "error_code:11071210 表示 ChinaDrm License，初始化数据解析错误。处理建议：联系开发人员"}
{"id": 371, "query": "error_code:11071211", "value": "error_code:11071211 表示 ChinaDrm License，JSON构建错误。处理建议：联系开发人员"}
{"id": 372, "query": "error_code:11071212", "value": "error_code:11071212 表示 ChinaDrm License，JSON构建错误。处理建议：联系开发人员"}
{"id": 373, "query": "error_code:11071301", "value": "error_code:11071301 表示 ChinaDrm Provision，生成随机数出错。处理建议：联系开发人员"}
{"id": 374, "query": "error_code:11071302", "value": "error_code:11071302 表示 ChinaDrm Provision，生成随机数出错。处理建议：联系开发人员"}
{"id": 375, "query": "error_code:11071303", "value": "error_code:11071303 表示 ChinaDrm Provision，生成随机数出错。处理建议：联系开发人员"}
{"id": 376, "query": "error_code:11071304", "value": "error_code:11071304 表示 ChinaDrm Provision，返回出错。处理建议：联系开发人员"}
{"id": 377, "query": "error_code:11071305", "value": "error_code:11071305 表示 ChinaDrm Provision，JSON解析失败。处理建议：联系开发人员"}
{"id": 378, "query": "error_code:11071306", "value": "error_code:11071306 表示 ChinaDrm Provision，数据解析失败。处理建议：联系开发人员"}
{"id": 379, "query": "error_code:11071307", "value": "error_code:11071307 表示 ChinaDrm Provision，内存不足。处理建议：联系开发人员"}
{"id": 380, "query": "error_code:11071308", "value": "error_code:11071308 表示 ChinaDrm Provision，内存不足。处理建议：联系开发人员"}
{"id": 381, "query": "error_code:11071309", "value": "error_code:11071309 表示 ChinaDrm Provision，参数错误。处理建议：联系开发人员"}
{"id": 382, "query": "error_code:11071401", "value": "error_code:11071401 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 383, "query": "error_code:11071402", "value": "error_code:11071402 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 384, "query": "error_code:11071403", "value": "error_code:11071403 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 385, "query": "error_code:11071404", "value": "error_code:11071404 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 386, "query": "error_code:11071405", "value": "error_code:11071405 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 387, "query": "error_code:11071406", "value": "error_code:11071406 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 388, "query": "error_code:11071407", "value": "error_code:11071407 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 389, "query": "error_code:11071408", "value": "error_code:11071408 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 390, "query": "error_code:11071409", "value": "error_code:11071409 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 391, "query": "error_code:11071410", "value": "error_code:11071410 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 392, "query": "error_code:11071411", "value": "error_code:11071411 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 393, "query": "error_code:11071412", "value": "error_code:11071412 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 394, "query": "error_code:11071413", "value": "error_code:11071413 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 395, "query": "error_code:11071414", "value": "error_code:11071414 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 396, "query": "error_code:11071415", "value": "error_code:11071415 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 397, "query": "error_code:11071416", "value": "error_code:11071416 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 398, "query": "error_code:11071417", "value": "error_code:11071417 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 399, "query": "error_code:11071418", "value": "error_code:11071418 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 400, "query": "error_code:11071419", "value": "error_code:11071419 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 401, "query": "error_code:11071420", "value": "error_code:11071420 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 402, "query": "error_code:11071421", "value": "error_code:11071421 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 403, "query": "error_code:11071422", "value": "error_code:11071422 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 404, "query": "error_code:11071423", "value": "error_code:11071423 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 405, "query": "error_code:11071424", "value": "error_code:11071424 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 406, "query": "error_code:11071425", "value": "error_code:11071425 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 407, "query": "error_code:11071426", "value": "error_code:11071426 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 408, "query": "error_code:11071427", "value": "error_code:11071427 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 409, "query": "error_code:11071428", "value": "error_code:11071428 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 410, "query": "error_code:11071429", "value": "error_code:11071429 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 411, "query": "error_code:11071430", "value": "error_code:11071430 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 412, "query": "error_code:11071431", "value": "error_code:11071431 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 413, "query": "error_code:11071432", "value": "error_code:11071432 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 414, "query": "error_code:11071433", "value": "error_code:11071433 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 415, "query": "error_code:11071434", "value": "error_code:11071434 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 416, "query": "error_code:11071435", "value": "error_code:11071435 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 417, "query": "error_code:11071436", "value": "error_code:11071436 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 418, "query": "error_code:11071437", "value": "error_code:11071437 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 419, "query": "error_code:11071438", "value": "error_code:11071438 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 420, "query": "error_code:11071439", "value": "error_code:11071439 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 421, "query": "error_code:11071440", "value": "error_code:11071440 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 422, "query": "error_code:11071441", "value": "error_code:11071441 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 423, "query": "error_code:11071442", "value": "error_code:11071442 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 424, "query": "error_code:11071443", "value": "error_code:11071443 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 425, "query": "error_code:11071444", "value": "error_code:11071444 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 426, "query": "error_code:11071445", "value": "error_code:11071445 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 427, "query": "error_code:11071446", "value": "error_code:11071446 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 428, "query": "error_code:11071447", "value": "error_code:11071447 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 429, "query": "error_code:11071448", "value": "error_code:11071448 表示 ChinaDrm OpenSSL，接口调用失败。处理建议：联系开发人员"}
{"id": 430, "query": "error_code:11071601", "value": "error_code:11071601 表示 ChinaDrm国密，算法不支持。处理建议：联系开发人员"}
{"id": 431, "query": "error_code:11071602", "value": "error_code:11071602 表示 ChinaDrm国密，SM2 sign失败。处理建议：联系开发人员"}
{"id": 432, "query": "error_code:11071603", "value": "error_code:11071603 表示 ChinaDrm国密，SM2 decrypt失败。处理建议：联系开发人员"}
{"id": 433, "query": "error_code:11071604", "value": "error_code:11071604 表示 ChinaDrm国密，SM2 encrypt失败。处理建议：联系开发人员"}
{"id": 434, "query": "error_code:11071605", "value": "error_code:11071605 表示 ChinaDrm国密，SM2 verify失败。处理建议：联系开发人员"}
{"id": 435, "query": "error_code:11071606", "value": "error_code:11071606 表示 ChinaDrm国密，非法参数。处理建议：联系开发人员"}
{"id": 436, "query": "error_code:11071701", "value": "error_code:11071701 表示 ChinaDrm License解析，加载失败。处理建议：联系开发人员"}
{"id": 437, "query": "error_code:11071702", "value": "error_code:11071702 表示 ChinaDrm License解析，加载失败。处理建议：联系开发人员"}
{"id": 438, "query": "error_code:11071703", "value": "error_code:11071703 表示 ChinaDrm License解析，加载失败。处理建议：联系开发人员"}
{"id": 439, "query": "error_code:11071704", "value": "error_code:11071704 表示 ChinaDrm License解析，非法参数。处理建议：联系开发人员"}
{"id": 440, "query": "error_code:11071705", "value": "error_code:11071705 表示 ChinaDrm License解析，非法参数。处理建议：联系开发人员"}
{"id": 441, "query": "error_code:11071751", "value": "error_code:11071751 表示 ChinaDrm License返回，JSON解析失败。处理建议：联系开发人员"}
{"id": 442, "query": "error_code:11071752", "value": "error_code:11071752 表示 ChinaDrm License返回，数据解析失败。处理建议：联系开发人员"}
{"id": 443, "query": "error_code:11071801", "value": "error_code:11071801 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 444, "query": "error_code:11071802", "value": "error_code:11071802 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 445, "query": "error_code:11071803", "value": "error_code:11071803 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 446, "query": "error_code:11071804", "value": "error_code:11071804 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 447, "query": "error_code:11071805", "value": "error_code:11071805 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 448, "query": "error_code:11071806", "value": "error_code:11071806 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 449, "query": "error_code:11071807", "value": "error_code:11071807 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 450, "query": "error_code:11071808", "value": "error_code:11071808 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 451, "query": "error_code:11071809", "value": "error_code:11071809 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 452, "query": "error_code:11071810", "value": "error_code:11071810 表示 ChinaDrm许可证存储模块出错。处理建议：联系开发人员"}
{"id": 453, "query": "error_code:11071901", "value": "error_code:11071901 表示 ChinaDrm密钥管理模块出错。处理建议：联系开发人员"}
{"id": 454, "query": "error_code:11071902", "value": "error_code:11071902 表示 ChinaDrm密钥管理模块出错。处理建议：联系开发人员"}
{"id": 455, "query": "error_code:11071903", "value": "error_code:11071903 表示 ChinaDrm密钥管理模块出错。处理建议：联系开发人员"}
{"id": 456, "query": "error_code:11071904", "value": "error_code:11071904 表示 ChinaDrm密钥管理模块出错。处理建议：联系开发人员"}
{"id": 457, "query": "error_code:11071905", "value": "error_code:11071905 表示 ChinaDrm密钥管理模块出错。处理建议：联系开发人员"}
{"id": 458, "query": "error_code:11071906", "value": "error_code:11071906 表示 ChinaDrm密钥管理模块出错。处理建议：联系开发人员"}
{"id": 459, "query": "error_code:11073001", "value": "error_code:11073001 表示 Unitend，解析#EXT-X-KEY出错。处理建议：联系开发人员"}
{"id": 460, "query": "error_code:11073002", "value": "error_code:11073002 表示 Unitend，申请内存不足。处理建议：联系开发人员"}
{"id": 461, "query": "error_code:11073003", "value": "error_code:11073003 表示 Unitend，密钥获取出错。处理建议：联系开发人员"}
{"id": 462, "query": "error_code:11074001", "value": "error_code:11074001 表示 Drm，不支持的DRM方案类型。处理建议：联系开发人员"}
{"id": 463, "query": "error_code:11074002", "value": "error_code:11074002 表示 Drm，内存不足。处理建议：联系开发人员"}
{"id": 464, "query": "error_code:11075002", "value": "error_code:11075002 表示 DrmManager，获取解密对象失败。处理建议：联系开发人员"}
{"id": 465, "query": "error_code:11079999", "value": "error_code:11079999 表示 Drm未知错误。处理建议：联系开发人员"}
{"id": 466, "query": "error_code:11080001", "value": "error_code:11080001 表示 curl，未知错误。处理建议：联系开发人员"}
{"id": 467, "query": "error_code:11080002", "value": "error_code:11080002 表示 curl，DNS解析失败。处理建议：检查网络是否正常"}
{"id": 468, "query": "error_code:11080003", "value": "error_code:11080003 表示 curl，connect失败。处理建议：检查网络是否正常"}
{"id": 469, "query": "error_code:11080004", "value": "error_code:11080004 表示 curl，connect超时。处理建议：检查网络是否正常"}
{"id": 470, "query": "error_code:11080005", "value": "error_code:11080005 表示 curl，send失败。处理建议：检查网络是否正常"}
{"id": 471, "query": "error_code:11080006", "value": "error_code:11080006 表示 curl，send超时。处理建议：检查网络是否正常"}
{"id": 472, "query": "error_code:11080007", "value": "error_code:11080007 表示 curl，recv失败。处理建议：检查网络是否正常"}
{"id": 473, "query": "error_code:11080008", "value": "error_code:11080008 表示 curl，recv超时。处理建议：检查网络是否正常"}
{"id": 474, "query": "error_code:11080009", "value": "error_code:11080009 表示 curl，非法地址。处理建议：检查地址是否可用"}
{"id": 475, "query": "error_code:11081000", "value": "error_code:11081000 表示 富媒体http错误码。处理建议：检查地址是否可用"}
{"id": 476, "query": "error_code:11082000", "value": "error_code:11082000 表示 富媒体json解析错误码。处理建议：检查json是否合法"}
{"id": 477, "query": "error_code:10000001", "value": "error_code:10000001 表示 未知异常。"}
{"id": 478, "query": "error_code:10000100", "value": "error_code:10000100 表示 媒体服务器死亡。。"}
{"id": 479, "query": "error_code:10000110", "value": "error_code:10000110 表示 超时。"}
{"id": 480, "query": "error_code:10000200", "value": "error_code:10000200 表示 视频已流式传输，并且其容器对于渐进播放无效，即视频的索引（例如moov atom）不在文件开头。。"}
{"id": 481, "query": "error_code:10001010", "value": "error_code:10001010 表示 比特流符合相关的编码标准或文件规范，但媒体框架不支持该功能。。"}
{"id": 482, "query": "error_code:10001007", "value": "error_code:10001007 表示 比特流不符合相关的编码标准或文件规范。"}
{"id": 483, "query": "error_code:10001004", "value": "error_code:10001004 表示 文件或网络相关的操作错误。"}
{"id": 484, "query": "error_code:10100101", "value": "error_code:10100101 表示 系统播放器通用错误。"}
{"id": 485, "query": "error_code:10100102", "value": "error_code:10100102 表示 系统播放器缓冲超时。"}
{"id": 486, "query": "error_code:10100103", "value": "error_code:10100103 表示 系统播放器无效播放源。"}
{"id": 487, "query": "error_code:10100104", "value": "error_code:10100104 表示 系统播放器切换清晰度发生错误。"}
{"id": 488, "query": "error_code:10100105", "value": "error_code:10100105 表示 系统播放器字幕Parser选择错误。"}
{"id": 489, "query": "error_code:10100106", "value": "error_code:10100106 表示 系统播放器字幕选择Index非法。"}
{"id": 490, "query": "error_code:10100000", "value": "error_code:10100000 表示 系统AVPlayer错误码起始值，会将AVPlayer返回的5位原始错误码拼接在一起。"}
{"id": 491, "query": "error_code:10101008", "value": "error_code:10101008 表示 无可用资源。处理建议：-12844 Playlist File not received"}
{"id": 492, "query": "error_code:10101100", "value": "error_code:10101100 表示 无法找到资源，对应http 404错误。"}
{"id": 493, "query": "error_code:10111800", "value": "error_code:10111800 表示 未知错误。处理建议：-12642 indicates that there was an error in the HLS playlist\n-12875 indicates a request timeout"}
{"id": 494, "query": "error_code:10111801", "value": "error_code:10111801 表示 没有足够内存去处理视频操作。"}
{"id": 495, "query": "error_code:10111803", "value": "error_code:10111803 表示 未捕获到数据，无法录制。"}
{"id": 496, "query": "error_code:10111804", "value": "error_code:10111804 表示 视频已在应用程序别处使用，无法从设备中获取。"}
{"id": 497, "query": "error_code:10111805", "value": "error_code:10111805 表示 没有获取到数据，录制失败。"}
{"id": 498, "query": "error_code:10111806", "value": "error_code:10111806 表示 视频来源和目标配置更改，录制停止。"}
{"id": 499, "query": "error_code:10111807", "value": "error_code:10111807 表示 磁盘已满，录制停止。"}
{"id": 500, "query": "error_code:10111808", "value": "error_code:10111808 表示 设备关闭或断开，录制停止。"}
{"id": 501, "query": "error_code:10111809", "value": "error_code:10111809 表示 源视频格式发生改变，录制停止。"}
{"id": 502, "query": "error_code:10111810", "value": "error_code:10111810 表示 达到文件最大持续时间，录制停止。"}
{"id": 503, "query": "error_code:10111811", "value": "error_code:10111811 表示 大小达到文件上限，录制停止。"}
{"id": 504, "query": "error_code:10111812", "value": "error_code:10111812 表示 视频发生中断，录制停止。"}
{"id": 505, "query": "error_code:10111813", "value": "error_code:10111813 表示 文件个数达到上限，录制停止。"}
{"id": 506, "query": "error_code:10111814", "value": "error_code:10111814 表示 无连接或未开启，设备无法打开。"}
{"id": 507, "query": "error_code:10111815", "value": "error_code:10111815 表示 设备由另一个应用程序在使用，无法打开。"}
{"id": 508, "query": "error_code:10111817", "value": "error_code:10111817 表示 设备正由另一个应用程序控制，无法更改设置。"}
{"id": 509, "query": "error_code:10111818", "value": "error_code:10111818 表示 被中断，录制停止。"}
{"id": 510, "query": "error_code:10111819", "value": "error_code:10111819 表示 视频服务不可用，操作无法完成。"}
{"id": 511, "query": "error_code:10111820", "value": "error_code:10111820 表示 输出无法完成。"}
{"id": 512, "query": "error_code:10111821", "value": "error_code:10111821 表示 有些视频资源无法解码，操作无法完成。"}
{"id": 513, "query": "error_code:10111822", "value": "error_code:10111822 表示 有些视频资源无法读取，操作无法完成。"}
{"id": 514, "query": "error_code:10111823", "value": "error_code:10111823 表示 相同名称的文件已经存在于同一位置，文件无法创建。"}
{"id": 515, "query": "error_code:10111824", "value": "error_code:10111824 表示 源视频包含空白，无法添加。"}
{"id": 516, "query": "error_code:10111825", "value": "error_code:10111825 表示 源视频在目标上的时间无效，无法添加。"}
{"id": 517, "query": "error_code:10111826", "value": "error_code:10111826 表示 源视频在目标上的开始时间无效，无法添加。"}
{"id": 518, "query": "error_code:10111827", "value": "error_code:10111827 表示 视频没有持续时间，无法添加。"}
{"id": 519, "query": "error_code:10111828", "value": "error_code:10111828 表示 视频格式无法识别，无法打开。"}
{"id": 520, "query": "error_code:10111829", "value": "error_code:10111829 表示 视频已损坏或格式不能识别，无法打开。"}
{"id": 521, "query": "error_code:10111830", "value": "error_code:10111830 表示 太多照片请求未完成，无法拍照。"}
{"id": 522, "query": "error_code:10111831", "value": "error_code:10111831 表示 该应用程序无权打开视频。"}
{"id": 523, "query": "error_code:10111832", "value": "error_code:10111832 表示 此刻在视频中无图像。"}
{"id": 524, "query": "error_code:10111833", "value": "error_code:10111833 表示 未找到该视频的解码器。"}
{"id": 525, "query": "error_code:10111834", "value": "error_code:10111834 表示 未找到需要的编码器。"}
{"id": 526, "query": "error_code:10111835", "value": "error_code:10111835 表示 用户无权播放视频。"}
{"id": 527, "query": "error_code:10111836", "value": "error_code:10111836 表示 应用程序无权播放视频。"}
{"id": 528, "query": "error_code:10111837", "value": "error_code:10111837 表示 IOS禁止尝试在后台捕获会话。"}
{"id": 529, "query": "error_code:10111838", "value": "error_code:10111838 表示 您尝试使用不支持的断言方式进行操作。"}
{"id": 530, "query": "error_code:10111839", "value": "error_code:10111839 表示 相应的解码器当前不可用。"}
{"id": 531, "query": "error_code:10111840", "value": "error_code:10111840 表示 相应的编码器当前不可用。"}
{"id": 532, "query": "error_code:10111841", "value": "error_code:10111841 表示 您尝试不被支持的视频合成操作。"}
{"id": 533, "query": "error_code:10111842", "value": "error_code:10111842 表示 您尝试用不被允许的断言方式去执行遵循参考的操作。"}
{"id": 534, "query": "error_code:10111843", "value": "error_code:10111843 表示 URL路径扩展无效。"}
{"id": 535, "query": "error_code:10111844", "value": "error_code:10111844 表示 捕获屏幕失败。"}
{"id": 536, "query": "error_code:10111845", "value": "error_code:10111845 表示 显示器处于不活动状态，屏幕捕获失败。"}
{"id": 537, "query": "error_code:10111846", "value": "error_code:10111846 表示 指定的torch等级有效，但当前不可用，可能由于设备过热。"}
{"id": 538, "query": "error_code:10111847", "value": "error_code:10111847 表示 应用程序执行读，写或导出时发生中断。"}
{"id": 539, "query": "error_code:10111848", "value": "error_code:10111848 表示 IOS设备不能播放当前视频。"}
{"id": 540, "query": "error_code:10111849", "value": "error_code:10111849 表示 视频资源不包含所需要的数据，且不能加载。"}
{"id": 541, "query": "error_code:10111850", "value": "error_code:10111850 表示 HTTP服务器发送的视频资源未如期配置，。"}
{"id": 542, "query": "error_code:nan", "value": "error_code:nan 表示 这可能意味着服务器不支持字节范围请求。"}
{"id": 543, "query": "error_code:10111852", "value": "error_code:10111852 表示 用户拒绝应用程序去获取视频资源。"}
{"id": 544, "query": "error_code:10111853", "value": "error_code:10111853 表示 解析失败。"}
{"id": 545, "query": "error_code:10111854", "value": "error_code:10111854 表示 文件类型不支持采样。"}
{"id": 546, "query": "error_code:10111855", "value": "error_code:10111855 表示 媒体无法被解码。"}
{"id": 547, "query": "error_code:10111856", "value": "error_code:10111856 表示 AirPlay控制器需要网络连接。"}
{"id": 548, "query": "error_code:10111857", "value": "error_code:10111857 表示 AirPlay接收器需要网络连接。"}
{"id": 549, "query": "error_code:10111858", "value": "error_code:10111858 表示 视频合成失败。"}
{"id": 550, "query": "error_code:10111859", "value": "error_code:10111859 表示 录像已在进行中。"}
{"id": 551, "query": "error_code:10111860", "value": "error_code:10111860 表示 创建contentkey请求失败。"}
{"id": 552, "query": "error_code:10111861", "value": "error_code:10111861 表示 不支持的输出设置。"}
{"id": 553, "query": "error_code:10111862", "value": "error_code:10111862 表示 原档视频信息。"}
{"id": 554, "query": "error_code:10111863", "value": "error_code:10111863 表示 内容不可用。"}
{"id": 555, "query": "error_code:10111864", "value": "error_code:10111864 表示 格式不支持。"}
{"id": 556, "query": "error_code:10111865", "value": "error_code:10111865 表示 AVErrorMalformedDepth。"}
{"id": 557, "query": "error_code:10111866", "value": "error_code:10111866 表示 内容未更新。"}
{"id": 558, "query": "error_code:10111867", "value": "error_code:10111867 表示 视频不再可播。"}
{"id": 559, "query": "error_code:10111868", "value": "error_code:10111868 表示 投屏无可兼容的设备。"}
{"id": 560, "query": "error_code:10111869", "value": "error_code:10111869 表示 没有源轨道。"}
{"id": 561, "query": "error_code:10111870", "value": "error_code:10111870 表示 不支持投射播放源。"}
{"id": 562, "query": "error_code:10111871", "value": "error_code:10111871 表示 前置操作不支持。"}
{"id": 563, "query": "error_code:10111872", "value": "error_code:10111872 表示 硬解过于耗时。"}
{"id": 564, "query": "error_code:10111873", "value": "error_code:10111873 表示 不支持的设备Active格式。"}
{"id": 565, "query": "error_code:13000101", "value": "error_code:13000101 表示 TP应用层未知错误。"}
{"id": 566, "query": "error_code:13000102", "value": "error_code:13000102 表示 TP应用层逻辑错误。"}
{"id": 567, "query": "error_code:130030", "value": "error_code:130030 表示 cgi错误，当前节目未付费。"}
{"id": 568, "query": "error_code:13100101", "value": "error_code:13100101 表示 无可用的播放器。"}
{"id": 569, "query": "error_code:13100102", "value": "error_code:13100102 表示 截图失败。"}
{"id": 570, "query": "error_code:14000100", "value": "error_code:14000100 表示 网络不稳定，频繁切换。"}
{"id": 571, "query": "error_code:14000101", "value": "error_code:14000101 表示 存在下载任务干扰。"}
{"id": 572, "query": "error_code:14000102", "value": "error_code:14000102 表示 网络硬件设备异常。"}
{"id": 573, "query": "error_code:14000103", "value": "error_code:14000103 表示 cdn连接超时。"}
{"id": 574, "query": "error_code:14000104", "value": "error_code:14000104 表示 cdn连接超时但是切换url最终连上。"}
{"id": 575, "query": "error_code:14000105", "value": "error_code:14000105 表示 接收超时。"}
{"id": 576, "query": "error_code:14000106", "value": "error_code:14000106 表示 接收超时但是切换url最终连上。"}
{"id": 577, "query": "error_code:14000107", "value": "error_code:14000107 表示 紧急时间内,接收速度不够(慢速比)。"}
{"id": 578, "query": "error_code:14000108", "value": "error_code:14000108 表示 紧急时间内,接收速度不够但是切换url成功。"}
{"id": 579, "query": "error_code:14000109", "value": "error_code:14000109 表示 码率抖动过大。"}
{"id": 580, "query": "error_code:14000110", "value": "error_code:14000110 表示 紧急时间内,p2p下载速度大于http下载速度。"}
{"id": 581, "query": "error_code:14000111", "value": "error_code:14000111 表示 下载时间过短。"}
{"id": 582, "query": "error_code:14000112", "value": "error_code:14000112 表示 紧急时间内,p2p上传速度影响下载。"}
{"id": 583, "query": "error_code:14000113", "value": "error_code:14000113 表示 任务创建失败。"}
{"id": 584, "query": "error_code:14001001", "value": "error_code:14001001 表示 找不到m3u8对应的ts序号。"}
{"id": 585, "query": "error_code:14001002", "value": "error_code:14001002 表示 一次播放的下载量超过文件大小的两倍，停止调度。"}
{"id": 586, "query": "error_code:14001003", "value": "error_code:14001003 表示 定时器状态异常。"}
{"id": 587, "query": "error_code:14001004", "value": "error_code:14001004 表示 m3u8 请求返回为空。"}
{"id": 588, "query": "error_code:14010001", "value": "error_code:14010001 表示 无效参数。"}
{"id": 589, "query": "error_code:14010002", "value": "error_code:14010002 表示 无效url。"}
{"id": 590, "query": "error_code:14010003", "value": "error_code:14010003 表示 dns解析失败。"}
{"id": 591, "query": "error_code:14010004", "value": "error_code:14010004 表示 创建socket失败。"}
{"id": 592, "query": "error_code:14010005", "value": "error_code:14010005 表示 连接失败。"}
{"id": 593, "query": "error_code:14010006", "value": "error_code:14010006 表示 连接超时。"}
{"id": 594, "query": "error_code:14010007", "value": "error_code:14010007 表示 发送失败。"}
{"id": 595, "query": "error_code:14010008", "value": "error_code:14010008 表示 分配内存失败。"}
{"id": 596, "query": "error_code:14010009", "value": "error_code:14010009 表示 接收失败。"}
{"id": 597, "query": "error_code:14010010", "value": "error_code:14010010 表示 接收超时。"}
{"id": 598, "query": "error_code:14010011", "value": "error_code:14010011 表示 buffer不够。"}
{"id": 599, "query": "error_code:14010012", "value": "error_code:14010012 表示 http头部溢出。"}
{"id": 600, "query": "error_code:14010013", "value": "error_code:14010013 表示 无效的http返回码。"}
{"id": 601, "query": "error_code:14010014", "value": "error_code:14010014 表示 无效的http文件大小。"}
{"id": 602, "query": "error_code:14010015", "value": "error_code:14010015 表示 无效的http内容长度。"}
{"id": 603, "query": "error_code:14010016", "value": "error_code:14010016 表示 无效的http域名。"}
{"id": 604, "query": "error_code:14010017", "value": "error_code:14010017 表示 返回码错误。"}
{"id": 605, "query": "error_code:14010018", "value": "error_code:14010018 表示 接收溢出。"}
{"id": 606, "query": "error_code:14010019", "value": "error_code:14010019 表示 服务端关闭连接。"}
{"id": 607, "query": "error_code:14010020", "value": "error_code:14010020 表示 socket错误。"}
{"id": 608, "query": "error_code:14010021", "value": "error_code:14010021 表示 超过最大重试次数。"}
{"id": 609, "query": "error_code:14010022", "value": "error_code:14010022 表示 其他未知错误。"}
{"id": 610, "query": "error_code:14010023", "value": "error_code:14010023 表示 尝试锁住失败。"}
{"id": 611, "query": "error_code:14010024", "value": "error_code:14010024 表示 连接中。"}
{"id": 612, "query": "error_code:14010025", "value": "error_code:14010025 表示 socket繁忙。"}
{"id": 613, "query": "error_code:14010026", "value": "error_code:14010026 表示 302重定向。"}
{"id": 614, "query": "error_code:14010027", "value": "error_code:14010027 表示 重定向到相同url。"}
{"id": 615, "query": "error_code:14010028", "value": "error_code:14010028 表示 无效文件类型。"}
{"id": 616, "query": "error_code:14010029", "value": "error_code:14010029 表示 慢速。"}
{"id": 617, "query": "error_code:14010030", "value": "error_code:14010030 表示 返回无效数据。"}
{"id": 618, "query": "error_code:14010031", "value": "error_code:14010031 表示 重定向到空连接。"}
{"id": 619, "query": "error_code:14010032", "value": "error_code:14010032 表示 gzip解压失败。"}
{"id": 620, "query": "error_code:14010033", "value": "error_code:14010033 表示 gzip数据错误。"}
{"id": 621, "query": "error_code:14020001", "value": "error_code:14020001 表示 重定向超过最大次数。"}
{"id": 622, "query": "error_code:14020002", "value": "error_code:14020002 表示 错误的返回码。"}
{"id": 623, "query": "error_code:14020003", "value": "error_code:14020003 表示 403错误码。"}
{"id": 624, "query": "error_code:14020004", "value": "error_code:14020004 表示 404错误码。"}
{"id": 625, "query": "error_code:14020005", "value": "error_code:14020005 表示 文件大小发生改变。"}
{"id": 626, "query": "error_code:14020006", "value": "error_code:14020006 表示 文件长度不匹配。"}
{"id": 627, "query": "error_code:14020007", "value": "error_code:14020007 表示 请求完整数据，返回部分数据场景。"}
{"id": 628, "query": "error_code:14020008", "value": "error_code:14020008 表示 请求range和返回range范围不一致的场景，比如CDN不支持range请求。"}
{"id": 629, "query": "error_code:14020009", "value": "error_code:14020009 表示 解析M3U8异常。"}
{"id": 630, "query": "error_code:14020010", "value": "error_code:14020010 表示 405错误码。"}
{"id": 631, "query": "error_code:14030001", "value": "error_code:14030001 表示 cgi无效参数。"}
{"id": 632, "query": "error_code:14030002", "value": "error_code:14030002 表示 cgi无效url。"}
{"id": 633, "query": "error_code:14030003", "value": "error_code:14030003 表示 cgidns解析失败。"}
{"id": 634, "query": "error_code:14030004", "value": "error_code:14030004 表示 cgi创建socket失败。"}
{"id": 635, "query": "error_code:14030005", "value": "error_code:14030005 表示 cgi连接失败。"}
{"id": 636, "query": "error_code:14030006", "value": "error_code:14030006 表示 cgi连接超时。"}
{"id": 637, "query": "error_code:14030007", "value": "error_code:14030007 表示 cgi发送失败。"}
{"id": 638, "query": "error_code:14030008", "value": "error_code:14030008 表示 cgi分配内存失败。"}
{"id": 639, "query": "error_code:14030009", "value": "error_code:14030009 表示 cgi接收失败。"}
{"id": 640, "query": "error_code:14030010", "value": "error_code:14030010 表示 cgi接收超时。"}
{"id": 641, "query": "error_code:14030011", "value": "error_code:14030011 表示 cgi buffer不够。"}
{"id": 642, "query": "error_code:14030012", "value": "error_code:14030012 表示 cgi http头部溢出。"}
{"id": 643, "query": "error_code:14030013", "value": "error_code:14030013 表示 cgi无效的http返回码。"}
{"id": 644, "query": "error_code:14030014", "value": "error_code:14030014 表示 cgi无效的http文件大小。"}
{"id": 645, "query": "error_code:14030015", "value": "error_code:14030015 表示 cgi无效的http域名。"}
{"id": 646, "query": "error_code:14030016", "value": "error_code:14030016 表示 cgi返回码错误。"}
{"id": 647, "query": "error_code:14030017", "value": "error_code:14030017 表示 cgi接收溢出。"}
{"id": 648, "query": "error_code:14030018", "value": "error_code:14030018 表示 cgi服务端关闭连接。"}
{"id": 649, "query": "error_code:14030019", "value": "error_code:14030019 表示 cgi socket错误。"}
{"id": 650, "query": "error_code:14030020", "value": "error_code:14030020 表示 cgi其他未知错误。"}
{"id": 651, "query": "error_code:14030021", "value": "error_code:14030021 表示 cgi重定向超过最大次数。"}
{"id": 652, "query": "error_code:14030022", "value": "error_code:14030022 表示 cgi 302重定向。"}
{"id": 653, "query": "error_code:14040001", "value": "error_code:14040001 表示 无效参数。"}
{"id": 654, "query": "error_code:14040002", "value": "error_code:14040002 表示 xml文件解析失败。"}
{"id": 655, "query": "error_code:14040003", "value": "error_code:14040003 表示 内容不可用。"}
{"id": 656, "query": "error_code:14040004", "value": "error_code:14040004 表示 URL不可用。"}
{"id": 657, "query": "error_code:14040005", "value": "error_code:14040005 表示 keyid不可用。"}
{"id": 658, "query": "error_code:14040006", "value": "error_code:14040006 表示 返回加密视频，但是加密key为空。"}
{"id": 659, "query": "error_code:1510001", "value": "error_code:1510001 表示 vfs错误base。"}
{"id": 660, "query": "error_code:1510001 + errno", "value": "error_code:1510001 + errno 表示 vfs基础错误 + linux errno。"}
{"id": 661, "query": "error_code:1510201", "value": "error_code:1510201 表示 写长度不一致。"}
{"id": 662, "query": "error_code:1510202", "value": "error_code:1510202 表示 VFS open失败。"}
{"id": 663, "query": "error_code:1510203", "value": "error_code:1510203 表示 输入无效参数。"}
{"id": 664, "query": "error_code:1510204", "value": "error_code:1510204 表示 offset非法，超过文件大小。"}
{"id": 665, "query": "error_code:1510205", "value": "error_code:1510205 表示 内存没有相应的ts数据。"}
{"id": 666, "query": "error_code:1510206", "value": "error_code:1510206 表示 缓存没有相应的ts数据。"}
{"id": 667, "query": "error_code:1510207", "value": "error_code:1510207 表示 ts未找到。"}
{"id": 668, "query": "error_code:1510208", "value": "error_code:1510208 表示 ts的大小未知。"}
{"id": 669, "query": "error_code:1510209", "value": "error_code:1510209 表示 内存不足。"}
{"id": 670, "query": "error_code:1510210", "value": "error_code:1510210 表示 sequenceID转成TsIndex失败。"}
{"id": 671, "query": "error_code:1510211", "value": "error_code:1510211 表示 tsIndex转成SequenceID失败。"}
{"id": 672, "query": "error_code:1510212", "value": "error_code:1510212 表示 写内存数据失败。"}
{"id": 673, "query": "error_code:1510213", "value": "error_code:1510213 表示 写数据offset非法。"}
{"id": 674, "query": "error_code:1510214", "value": "error_code:1510214 表示 离线播放未找到m3u8。"}
{"id": 675, "query": "error_code:16000999", "value": "error_code:16000999 表示 未知错误。"}
{"id": 676, "query": "error_code:16001000", "value": "error_code:16001000 表示 成功。"}
{"id": 677, "query": "error_code:16001001", "value": "error_code:16001001 表示 操作没有权限。"}
{"id": 678, "query": "error_code:16001002", "value": "error_code:16001002 表示 没有该文件或者目录。"}
{"id": 679, "query": "error_code:16001003", "value": "error_code:16001003 表示 没有该进程。"}
{"id": 680, "query": "error_code:16001004", "value": "error_code:16001004 表示 终端系统调用。"}
{"id": 681, "query": "error_code:16001005", "value": "error_code:16001005 表示 IO错误。"}
{"id": 682, "query": "error_code:16001006", "value": "error_code:16001006 表示 没有该设备或者地址。"}
{"id": 683, "query": "error_code:16001007", "value": "error_code:16001007 表示 参数列表过长。"}
{"id": 684, "query": "error_code:16001008", "value": "error_code:16001008 表示 运行格式错误。"}
{"id": 685, "query": "error_code:16001009", "value": "error_code:16001009 表示 文件编号错误。"}
{"id": 686, "query": "error_code:16001010", "value": "error_code:16001010 表示 没有子进程。"}
{"id": 687, "query": "error_code:16001011", "value": "error_code:16001011 表示 资源死锁会发生。"}
{"id": 688, "query": "error_code:16001012", "value": "error_code:16001012 表示 内存溢出。"}
{"id": 689, "query": "error_code:16001013", "value": "error_code:16001013 表示 没有权限。"}
{"id": 690, "query": "error_code:16001014", "value": "error_code:16001014 表示 地址错误。"}
{"id": 691, "query": "error_code:16001015", "value": "error_code:16001015 表示 需要块设备。"}
{"id": 692, "query": "error_code:16001016", "value": "error_code:16001016 表示 设备或者资源繁忙。"}
{"id": 693, "query": "error_code:16001017", "value": "error_code:16001017 表示 文件已存在。"}
{"id": 694, "query": "error_code:16001018", "value": "error_code:16001018 表示 跨设备链接。"}
{"id": 695, "query": "error_code:16001019", "value": "error_code:16001019 表示 没有该设备。"}
{"id": 696, "query": "error_code:16001020", "value": "error_code:16001020 表示 不是目录。"}
{"id": 697, "query": "error_code:16001021", "value": "error_code:16001021 表示 是目录。"}
{"id": 698, "query": "error_code:16001022", "value": "error_code:16001022 表示 无效参数。"}
{"id": 699, "query": "error_code:16001023", "value": "error_code:16001023 表示 文件表溢出。"}
{"id": 700, "query": "error_code:16001024", "value": "error_code:16001024 表示 打开文件过多。"}
{"id": 701, "query": "error_code:16001025", "value": "error_code:16001025 表示 不是打字机。"}
{"id": 702, "query": "error_code:16001026", "value": "error_code:16001026 表示 文本文件忙。"}
{"id": 703, "query": "error_code:16001027", "value": "error_code:16001027 表示 文件过大。"}
{"id": 704, "query": "error_code:16001028", "value": "error_code:16001028 表示 设备上没有剩余空间。"}
{"id": 705, "query": "error_code:16001029", "value": "error_code:16001029 表示 非法查询。"}
{"id": 706, "query": "error_code:16001030", "value": "error_code:16001030 表示 只读文件系统。"}
{"id": 707, "query": "error_code:16001031", "value": "error_code:16001031 表示 链接太多。"}
{"id": 708, "query": "error_code:16001032", "value": "error_code:16001032 表示 管道破裂。"}
{"id": 709, "query": "error_code:16001033", "value": "error_code:16001033 表示 参数超出函数域。"}
{"id": 710, "query": "error_code:16001034", "value": "error_code:16001034 表示 结果无法表示。"}
{"id": 711, "query": "error_code:16001035", "value": "error_code:16001035 表示 重试(在Linux下面是11，即16001011）。"}
{"id": 712, "query": "error_code:16001062", "value": "error_code:16001062 表示 太多符号链接(在Linux下面是40，即16001040）。"}
{"id": 713, "query": "error_code:16001063", "value": "error_code:16001063 表示 文件名太长(在Linux下面是36，即16001036）。"}
{"id": 714, "query": "error_code:16001066", "value": "error_code:16001066 表示 目录非空(在Linux下面是39，即16001039）。"}
{"id": 715, "query": "error_code:16001077", "value": "error_code:16001077 表示 没有可用的记录锁(在Linux下面是37，即16001037）。"}
{"id": 716, "query": "error_code:16001078", "value": "error_code:16001078 表示 函数未实现(在Linux下面是38，即16001038）。"}
{"id": 717, "query": "error_code:16010000", "value": "error_code:16010000 表示 User custom error code。"}
{"id": 718, "query": "error_code:16010001", "value": "error_code:16010001 表示 接口未实现。"}
{"id": 719, "query": "error_code:16010002", "value": "error_code:16010002 表示 资源还没加载。"}
{"id": 720, "query": "error_code:16010003", "value": "error_code:16010003 表示 文件没打开。"}
{"id": 721, "query": "error_code:16010004", "value": "error_code:16010004 表示 文件数据还没准备好。"}
{"id": 722, "query": "error_code:16010005", "value": "error_code:16010005 表示 文件打开失败。"}
{"id": 723, "query": "error_code:16010006", "value": "error_code:16010006 表示 创建文件失败。"}
{"id": 724, "query": "error_code:16010007", "value": "error_code:16010007 表示 文件正在使用。"}
{"id": 725, "query": "error_code:16010008", "value": "error_code:16010008 表示 文件没有下载完成。"}
{"id": 726, "query": "error_code:16010009", "value": "error_code:16010009 表示 文件系统没有初始化。"}
{"id": 727, "query": "error_code:16010010", "value": "error_code:16010010 表示 SetFilesize 时，文件大小不匹配。"}
{"id": 728, "query": "error_code:16010011", "value": "error_code:16010011 表示 文件没有完成写入。"}
{"id": 729, "query": "error_code:16010012", "value": "error_code:16010012 表示 打开文件存储配置文件失败。"}
{"id": 730, "query": "error_code:16010013", "value": "error_code:16010013 表示 文件已经打开了。"}
{"id": 731, "query": "error_code:16010014", "value": "error_code:16010014 表示 文件大小为0，无法打开。"}
{"id": 732, "query": "error_code:1711001", "value": "error_code:1711001 表示 flv写数据时，分片没有初始化大小。"}
{"id": 733, "query": "error_code:1711002", "value": "error_code:1711002 表示 flv写数据时，需要写的数据大于分片剩余长度，但是数据未正确写。"}
{"id": 734, "query": "error_code:1711003", "value": "error_code:1711003 表示 flv写数据时，发生未知错误。"}
{"id": 735, "query": "error_code:1711004", "value": "error_code:1711004 表示 flv读数据时，没有读到数据。"}
{"id": 736, "query": "error_code:1711005", "value": "error_code:1711005 表示 flv读数据时，读到的数据不对。"}
{"id": 737, "query": "error_code:1711006", "value": "error_code:1711006 表示 flv读数据时，未获取到需要开始读取内存的地方。"}
{"id": 738, "query": "error_code:1711007", "value": "error_code:1711007 表示 flv读数据时，cache列表为空。"}
{"id": 739, "query": "error_code:1711008", "value": "error_code:1711008 表示 flv读数据时，clip索引错误。"}
{"id": 740, "query": "error_code:1711009", "value": "error_code:1711009 表示 offset非法，超过文件大小。"}
{"id": 741, "query": "error_code:1711010", "value": "error_code:1711010 表示 flv读取clip发生未知错误。"}
{"id": 742, "query": "error_code:1711011", "value": "error_code:1711011 表示 输入无效参数。"}
{"id": 743, "query": "error_code:1711012", "value": "error_code:1711012 表示 获取cache错误。"}
{"id": 744, "query": "error_code:1711013", "value": "error_code:1711013 表示 flv读数据时，发生未知错误。"}
{"id": 745, "query": "error_code:1712001", "value": "error_code:1712001 表示 flv processor 内存不足。"}
{"id": 746, "query": "error_code:1712002", "value": "error_code:1712002 表示 flv processor 探测出错。"}
{"id": 747, "query": "error_code:1712003", "value": "error_code:1712003 表示 flv processor Parser出错。"}
{"id": 748, "query": "error_code:1712004", "value": "error_code:1712004 表示 flv processor 没有 header tag。"}
{"id": 749, "query": "error_code:1712005", "value": "error_code:1712005 表示 flv processor 去重失败。"}
{"id": 750, "query": "error_code:1713000", "value": "error_code:1713000 表示 flv解析数据时，未知错误。"}
{"id": 751, "query": "error_code:1713001", "value": "error_code:1713001 表示 flv解析数据时，输入的buffer为空。"}
{"id": 752, "query": "error_code:1713002", "value": "error_code:1713002 表示 flv解析数据时，flv头部长度错误。"}
{"id": 753, "query": "error_code:1713003", "value": "error_code:1713003 表示 flv解析数据时，探测不是flv数据格式包。"}
{"id": 754, "query": "error_code:1713004", "value": "error_code:1713004 表示 flv解析数据时，flv previous tag length错误。"}
{"id": 755, "query": "error_code:1713005", "value": "error_code:1713005 表示 flv解析数据时，flv script tag length错误。"}
{"id": 756, "query": "error_code:1713006", "value": "error_code:1713006 表示 flv解析数据时，flv video tag body length错误。"}
{"id": 757, "query": "error_code:1713007", "value": "error_code:1713007 表示 flv解析数据时，flv audio tag body length错误。"}
{"id": 758, "query": "error_code:1713008", "value": "error_code:1713008 表示 flv解析数据时，flv tag 未知格式。"}
{"id": 759, "query": "error_code:1713009", "value": "error_code:1713009 表示 flv解析数据时，没有解析完成。"}
{"id": 760, "query": "error_code:1714001", "value": "error_code:1714001 表示 flv downloader启动失败。"}
{"id": 761, "query": "error_code:1714002", "value": "error_code:1714002 表示 flv downloader数据offset和recvSize不一致。"}
{"id": 762, "query": "error_code:12000001", "value": "error_code:12000001 表示 加载Monet库失败。"}
{"id": 763, "query": "error_code:12000002", "value": "error_code:12000002 表示 初始化Monet引擎失败。"}
{"id": 764, "query": "error_code:12000012", "value": "error_code:12000012 表示 Monet GL处理无效参数。"}
{"id": 765, "query": "error_code:12000015", "value": "error_code:12000015 表示 Monet GL处理内存不足。"}
{"id": 766, "query": "error_code:12010001", "value": "error_code:12010001 表示 模型加载失败。"}
{"id": 767, "query": "error_code:12010002", "value": "error_code:12010002 表示 创建GL数据失败。"}
{"id": 768, "query": "error_code:12021001", "value": "error_code:12021001 表示 杜比视界颜色管理对象未创建。"}
{"id": 769, "query": "error_code:12021002", "value": "error_code:12021002 表示 杜比视界颜色管理对象未初始化。"}
{"id": 770, "query": "error_code:12021003", "value": "error_code:12021003 表示 杜比视界颜色管理对象初始化失败。"}
{"id": 771, "query": "error_code:12021100", "value": "error_code:12021100 表示 杜比视界DVMA库处理失败。"}
{"id": 772, "query": "error_code:12021101", "value": "error_code:12021101 表示 杜比视界DVMA库缺少参数。"}
{"id": 773, "query": "error_code:12021102", "value": "error_code:12021102 表示 杜比视界DVMA库无效参数ID。"}
{"id": 774, "query": "error_code:12021103", "value": "error_code:12021103 表示 杜比视界DVMA库缺少静态元数据。"}
{"id": 775, "query": "error_code:12021104", "value": "error_code:12021104 表示 杜比视界DVMA库缺少Combo Lut。"}
{"id": 776, "query": "error_code:12021105", "value": "error_code:12021105 表示 杜比视界DVMA库缺少配置config buffer。"}
{"id": 777, "query": "error_code:12021106", "value": "error_code:12021106 表示 杜比视界DVMA库LP 模式不支持。"}
{"id": 778, "query": "error_code:12021107", "value": "error_code:12021107 表示 杜比视界DVMA库设置视频分辨率时缺少参数。"}
{"id": 779, "query": "error_code:12021108", "value": "error_code:12021108 表示 杜比视界DVMA库不支持的杜比profile。"}
{"id": 780, "query": "error_code:12021109", "value": "error_code:12021109 表示 杜比视界DVMA库缺少view port。"}
{"id": 781, "query": "error_code:12021110", "value": "error_code:12021110 表示 杜比视界DVMA库设置渲染描述符时缺少参数。"}
{"id": 782, "query": "error_code:12021111", "value": "error_code:12021111 表示 杜比视界DVMA库未知错误。"}
{"id": 783, "query": "error_code:70011000", "value": "error_code:70011000 表示 未知错误。"}
{"id": 784, "query": "error_code:70011100", "value": "error_code:70011100 表示 H5 视频取回过程被用户终止。"}
{"id": 785, "query": "error_code:70011101", "value": "error_code:70011101 表示 H5 当视频下载时发生错误。"}
{"id": 786, "query": "error_code:70011102", "value": "error_code:70011102 表示 H5 当视频解码时发生错误。"}
{"id": 787, "query": "error_code:70011103", "value": "error_code:70011103 表示 H5 不支持该音频/视频。"}
{"id": 788, "query": "error_code:70011104", "value": "error_code:70011104 表示 H5 视频链接请求超时。"}
{"id": 789, "query": "error_code:70011111", "value": "error_code:70011111 表示 播放中卡死（缓冲区无变化等情况）。"}
{"id": 790, "query": "error_code:70011112", "value": "error_code:70011112 表示 传入空url播放。"}
{"id": 791, "query": "error_code:70011113", "value": "error_code:70011113 表示 找不到播放内核。"}
{"id": 792, "query": "error_code:70020000", "value": "error_code:70020000 表示 未知错误。"}
{"id": 793, "query": "error_code:70020001", "value": "error_code:70020001 表示 mp4的range请求有返回，但是xhr状态码错误。"}
{"id": 794, "query": "error_code:70020002", "value": "error_code:70020002 表示 mp4的range请求网络错误。"}
{"id": 795, "query": "error_code:70020003", "value": "error_code:70020003 表示 mse模块出错（例如初始化MediaSource的codec不正确，sourcebuffer中append数据出错等）。"}
{"id": 796, "query": "error_code:70020004", "value": "error_code:70020004 表示 demux/remux过程中出错。"}
{"id": 797, "query": "error_code:70030000", "value": "error_code:70030000 表示 未知错误。"}
{"id": 798, "query": "error_code:70030001", "value": "error_code:70030001 表示 EME模块错误。"}
{"id": 799, "query": "error_code:70030002", "value": "error_code:70030002 表示 缓冲区数据停止导致卡住。"}
{"id": 800, "query": "error_code:70030003", "value": "error_code:70030003 表示 解析m3u8文件没有可用codec。"}
{"id": 801, "query": "error_code:70030004", "value": "error_code:70030004 表示 分片解密出错。"}
{"id": 802, "query": "error_code:70030005", "value": "error_code:70030005 表示 分片解析出错。"}
{"id": 803, "query": "error_code:70030006", "value": "error_code:70030006 表示 hls.js抛出其他异常。"}
{"id": 804, "query": "error_code:70030007", "value": "error_code:70030007 表示 master-m3u8加载超时。"}
{"id": 805, "query": "error_code:70030008", "value": "error_code:70030008 表示 master-m3u8加载出错。"}
{"id": 806, "query": "error_code:70030009", "value": "error_code:70030009 表示 level-m3u8加载超时。"}
{"id": 807, "query": "error_code:70030010", "value": "error_code:70030010 表示 level-m3u8加载出错。"}
{"id": 808, "query": "error_code:70030011", "value": "error_code:70030011 表示 分片文件加载超时。"}
{"id": 809, "query": "error_code:70030012", "value": "error_code:70030012 表示 分片文件加载出错。"}
{"id": 810, "query": "error_code:70040000", "value": "error_code:70040000 表示 未知错误。"}
{"id": 811, "query": "error_code:70040001", "value": "error_code:70040001 表示 没有getinfo插件。"}
{"id": 812, "query": "error_code:70040002", "value": "error_code:70040002 表示 广告播放结束出错的默认错误。"}
{"id": 813, "query": "error_code:70040003", "value": "error_code:70040003 表示 广告信息请求出错的默认错误。"}
{"id": 814, "query": "error_code:70013030", "value": "error_code:70013030 表示 CGI内部错误。"}
{"id": 815, "query": "error_code:70013040", "value": "error_code:70013040 表示 未知错误。"}
{"id": 816, "query": "error_code:70013041", "value": "error_code:70013041 表示 服务解包失败。"}
{"id": 817, "query": "error_code:70013042", "value": "error_code:70013042 表示 服务打包失败。"}
{"id": 818, "query": "error_code:70013050", "value": "error_code:70013050 表示 CGI初始化失败。"}
{"id": 819, "query": "error_code:70013060", "value": "error_code:70013060 表示 filename与vid format不对应。"}
{"id": 820, "query": "error_code:70013061", "value": "error_code:70013061 表示 vid不合法。"}
{"id": 821, "query": "error_code:70013062", "value": "error_code:70013062 表示 文件状态不合法。"}
{"id": 822, "query": "error_code:70013064", "value": "error_code:70013064 表示 访问付费服务出错。"}
{"id": 823, "query": "error_code:70013067", "value": "error_code:70013067 表示 文件格式出错。"}
{"id": 824, "query": "error_code:70013069", "value": "error_code:70013069 表示 没有可播格式。"}
{"id": 825, "query": "error_code:70013074", "value": "error_code:70013074 表示 分片号超出范围。"}
{"id": 826, "query": "error_code:70013076", "value": "error_code:70013076 表示 动态码率格式错误。"}
{"id": 827, "query": "error_code:70013080", "value": "error_code:70013080 表示 IP版权限制。"}
{"id": 828, "query": "error_code:70013083", "value": "error_code:70013083 表示 未付费。"}
{"id": 829, "query": "error_code:70013084.1", "value": "error_code:70013084.1 表示 IPcgi访问视频type限制(通常是第三方鉴权 未传鉴权ID，或者播放的视频类型不在许可范围内)。"}
{"id": 830, "query": "error_code:70013085", "value": "error_code:70013085 表示 Ckey校验失败。"}
{"id": 831, "query": "error_code:70013087", "value": "error_code:70013087 表示 vids数量过多。"}
{"id": 832, "query": "error_code:70013090", "value": "error_code:70013090 表示 广告type不合法。"}
{"id": 833, "query": "error_code:70013091", "value": "error_code:70013091 表示 1080P未付费。"}
{"id": 834, "query": "error_code:70013092", "value": "error_code:70013092 表示 视频禁止下载。"}
{"id": 835, "query": "error_code:70013094", "value": "error_code:70013094 表示 多IP付费打击（付费）。"}
{"id": 836, "query": "error_code:70013100", "value": "error_code:70013100 表示 站外限制播放。"}
{"id": 837, "query": "error_code:70014001", "value": "error_code:70014001 表示 CGI失败(404、500等)。"}
{"id": 838, "query": "error_code:70014002", "value": "error_code:70014002 表示 CGI跨域失败。"}
{"id": 839, "query": "error_code:70014003", "value": "error_code:70014003 表示 CGI超时错误。"}
{"id": 840, "query": "error_code:70014004", "value": "error_code:70014004 表示 CGI有返回但是无法解析。"}
{"id": 841, "query": "error_code:70014005", "value": "error_code:70014005 表示 getinfo插件内部错误（代码异常等）。"}
{"id": 842, "query": "error_code:-1", "value": "error_code:-1 表示 播放失败：视频插件未安装失败。"}
{"id": 843, "query": "error_code:-2", "value": "error_code:-2 表示 播放失败：没有可用网络"}
{"id": 844, "query": "error_code:1401002", "value": "error_code:1401002 表示 播放器内部无网络时回调的错误码"}
{"id": 845, "query": "error_code:-10199 ", "value": "error_code:-10199  表示 虎牙视频强制换链失败。"}
