import json
from typing import Tuple, Optional

from common.logs.logger import app_logger
from common.error.detailed_value_error import raise_value_error, ErrorCode
from common.client.iwiki_client import IWikiClient
from common.tools.text_parse_utils import TextParseUtils
from common.tools.file_utils import append_to_file
from common.client.hunyuan_client import HunyuanClient

class UserIntentParser:
    @staticmethod
    def parse_user_intent(query: str, ticket_id: Optional[str]) -> Tuple[str, str]:
        """
        解析用户意图

        :param query: 用户问题
        :param ticket_id: 工单ID，可选，用于日志写入
        :return: (user_query_scene, url)
        """
        # 查询配置文件，获取已有预设场景
        iwiki_client = IWikiClient()
        body_content = iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014598562")
        scenes = []
        if body_content and body_content.lstrip().startswith('```'):
            # 提取代码块内容，得到json配置
            json_data = TextParseUtils.extract_code_block(body_content)
            json_items = TextParseUtils.parse_json_lines(json_data)
            scenes = [item.get('scene', '') for item in json_items]

        if not scenes:
            # raise ValueError('预设场景配置为空')
            raise_value_error(ErrorCode.PRESET_SCENES_EMPTY, message="预设场景配置为空/未成功打开预设场景配置")
        # 增加其他场景
        scenes.append("其他")

        # 用户意图场景及对应的 prompt iwiki 链接
        user_query_scene = ''
        url = ''
        user_intent_prompt = iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014606396")
        if not (user_intent_prompt and user_intent_prompt.lstrip().startswith('```')):
            # raise ValueError('未找到用户意图分析Prompt')
            raise_value_error(ErrorCode.USER_INTENT_PROMPT_NOT_FOUND, message="未找到用户意图分析Prompt")

        user_intent_prompt = TextParseUtils.extract_code_block(user_intent_prompt)
        user_intent_prompt = user_intent_prompt.format(query=query, scenes=scenes)

        items = UserIntentParser._request_model_for_user_intent(user_intent_prompt)
        if items is None:
            app_logger.info("流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="流式结果为空")

        for result in items:
            if result['type'] == 'all_answer':
                data = json.loads(result['data'])
                user_query_scene = data.get("issue_scene")
                if user_query_scene and user_query_scene != "其他":
                    url = TextParseUtils.get_field_by_scene(json_items, user_query_scene, "iwiki_url")
                    if ticket_id:
                        append_to_file(ticket_id, f'>>> 识别到的场景 以及 对应的prompt文件链接：\n{user_query_scene} -> {url}\n')
                    return user_query_scene, url
                else:
                    # raise ValueError('未预设场景，请找 lichenlin 增加日志分析场景')
                    raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message="未预设场景，请找 lichenlin 增加日志分析场景")

        # 如果没有返回，抛异常
        # raise ValueError('未识别到有效的用户意图场景')
        raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message="未识别到有效的用户意图场景")
    
    # 用户意图 model
    @staticmethod
    def _request_model_for_user_intent(prompt):
        app_logger.info(prompt)
        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/chat/completions"
        model = "70B-Dense-SFT-32K"  # DeepSeek-R1
        wsid = "10697"
        token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
        is_stream = False
        hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream)
        for result in hunyuan_client.request(prompt):
            yield {"data": result['data'], "type": result['type']}
